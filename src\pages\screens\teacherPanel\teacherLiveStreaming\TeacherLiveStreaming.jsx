

import { useState, useEffect, useRef } from 'react';
import {
  Room,
  RoomEvent,
  createLocalVideoTrack,
  createLocalAudioTrack,
  createLocalScreenTracks
} from 'livekit-client';
import { useDispatch, useSelector } from 'react-redux';
import { motion, AnimatePresence } from 'framer-motion';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import {
  useStartEnhancedStreamMutation,
  useStopEnhancedStreamMutation,
  useSendChatMessageMutation,
  useLazyGetChatHistoryQuery,
  useUploadCbtPaperMutation,
  setStreamingData,
  setIsStreaming,
  setError,
  setChatMessages,
  setChatLoading,
  setChatError,
  clearStreamingData
} from './teacherLiveStreaming.slice';

const TeacherLiveStreaming = () => {
  const dispatch = useDispatch();
  const { isStreaming, streamToken, roomName, error, chatMessages, chatLoading, chatError } =
    useSelector((state) => state.liveStreaming || {});

  const [startStream] = useStartEnhancedStreamMutation();
  const [stopStream] = useStopEnhancedStreamMutation();
  const [sendChatMessageMutation] = useSendChatMessageMutation();
  const [getChatHistory] = useLazyGetChatHistoryQuery();
  const [uploadCbtPaper] = useUploadCbtPaperMutation();

  // LiveKit states
  const [livekitRoom, setLivekitRoom] = useState(null);
  const [livekitConnected, setLivekitConnected] = useState(false);
  const [livekitToken, setLivekitToken] = useState(null);
  const [livekitUrl, setLivekitUrl] = useState(null);
  const [sessionId, setSessionId] = useState(null);

  // Media states
  const [localVideoTrack, setLocalVideoTrack] = useState(null);
  const [localAudioTrack, setLocalAudioTrack] = useState(null);
  const [localScreenTrack, setLocalScreenTrack] = useState(null);
  const [localScreenAudioTrack, setLocalScreenAudioTrack] = useState(null);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [previewVideoTrack, setPreviewVideoTrack] = useState(null);
  const [cameraPermissionGranted, setCameraPermissionGranted] = useState(false);
  const [screenStream, setScreenStream] = useState(null);

  // UI states
  const [streamStatus, setStreamStatus] = useState('Ready to start streaming');
  const [participants, setParticipants] = useState([]);
  const [quality, setQuality] = useState('medium');
  const [activeSidebarTab, setActiveSidebarTab] = useState('status'); // New state for sidebar tabs: 'status', 'viewers'

  // Chat states
  const [newMessage, setNewMessage] = useState('');
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [unreadMessages, setUnreadMessages] = useState(0);
  const [joinedViewers, setJoinedViewers] = useState([]);

  // Socket.IO states
  const [socket, setSocket] = useState(null);
  const [socketConnected, setSocketConnected] = useState(false);

  // Quiz states
  const [showQuizUpload, setShowQuizUpload] = useState(false);
  const [quizFile, setQuizFile] = useState(null);
  const [isUploadingQuiz, setIsUploadingQuiz] = useState(false);

  // Transcription states (changed from translation)
  const [isTranscriptionEnabled, setIsTranscriptionEnabled] = useState(false);
  const [speechRecognition, setSpeechRecognition] = useState(null);
  const [currentTranscription, setCurrentTranscription] = useState('');
  const [sourceLanguage, setSourceLanguage] = useState('en');
  const [transcriptionStatus, setTranscriptionStatus] = useState('');
  const [microphoneActive, setMicrophoneActive] = useState(false);
  const [livekitTextStream, setLivekitTextStream] = useState(null);

  // Refs
  const videoRef = useRef(null);
  const screenVideoRef = useRef(null);
  const pipCameraRef = useRef(null);
  const livekitRoomRef = useRef(null);
  const sharedMicrophoneStreamRef = useRef(null);

  // Available languages for translation
  const availableSourceLanguages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'ta', name: 'Tamil', flag: '🇮🇳' },
    { code: 'hi', name: 'Hindi', flag: '🇮🇳' },
    { code: 'te', name: 'Telugu', flag: '🇮🇳' },
    { code: 'kn', name: 'Kannada', flag: '🇮🇳' }
  ];

  // Check secure context
  useEffect(() => {
    if (!window.isSecureContext) {
      console.error('❌ App not running in secure context. Camera access requires HTTPS.');
      setStreamStatus('Error: Camera access requires HTTPS.');
      dispatch(setError('Camera access requires a secure context (HTTPS).'));
    }
  }, []);

  // Initialize session ID on mount
  useEffect(() => {
    const userId = sessionStorage.getItem('userId');
    const newSessionId = `teacher_${userId}_${Date.now()}`;
    setSessionId(newSessionId);
  }, []);

  // Initialize camera preview when streaming starts
  useEffect(() => {
    if (isStreaming) {
      initializeCameraPreview();
    }
    return () => {
      if (!isStreaming && previewVideoTrack) {
        previewVideoTrack.stop();
        setPreviewVideoTrack(null);
      }
    };
  }, [isStreaming]);

  // Attach video track when previewVideoTrack changes
  useEffect(() => {
    if (previewVideoTrack && videoRef.current && !isScreenSharing) {
      previewVideoTrack.attach(videoRef.current);
      console.log('✅ Preview video track attached via useEffect');
    }
  }, [previewVideoTrack, isScreenSharing]);

  // Connect to LiveKit room when token and URL are available
  useEffect(() => {
    if (livekitToken && livekitUrl && !livekitRoom) {
      connectToLiveKitRoom();
    }
  }, [livekitToken, livekitUrl]);

  // Publish camera track when localVideoTrack is available and room is connected
  useEffect(() => {
    const publishCameraTrack = async () => {
      if (localVideoTrack && livekitRoom && livekitConnected) {
        try {
          const publishedTracks = Array.from(livekitRoom.localParticipant.videoTracks.values());
          const existingPublication = publishedTracks.find(
            (pub) => pub.source === 'camera' || pub.trackName === 'teacher_camera'
          );
          if (!existingPublication) {
            await livekitRoom.localParticipant.publishTrack(localVideoTrack, {
              source: 'camera',
              name: 'teacher_camera'
            });
            console.log('✅ Camera track published to LiveKit via useEffect with camera source');
          } else {
            console.log('ℹ️ Camera track already published');
          }
        } catch (err) {
          console.error('❌ Failed to publish camera track via useEffect:', err);
        }
      }
    };
    publishCameraTrack();
  }, [localVideoTrack, livekitRoom, livekitConnected]);

  // Handle screen stream changes
  useEffect(() => {
    if (screenStream && screenVideoRef.current && isScreenSharing) {
      console.log('🖥️ Setting screen stream to video element');
      screenVideoRef.current.srcObject = screenStream;
      screenVideoRef.current.play().catch((err) => {
        console.warn('⚠️ Screen video autoplay failed:', err);
      });
    }
  }, [screenStream, isScreenSharing]);

  // Handle PiP camera attachment during screen sharing
  useEffect(() => {
    if (isScreenSharing && previewVideoTrack && pipCameraRef.current) {
      try {
        if (pipCameraRef.current.srcObject) {
          pipCameraRef.current.srcObject = null;
        }
        previewVideoTrack.attach(pipCameraRef.current);
        console.log('✅ Camera attached to PiP via useEffect');
        pipCameraRef.current.play().catch((playErr) => {
          console.warn('⚠️ PiP camera autoplay failed:', playErr);
        });
      } catch (attachErr) {
        console.warn('⚠️ Failed to attach camera to PiP via useEffect:', attachErr);
        try {
          const mediaStream = new MediaStream([previewVideoTrack.mediaStreamTrack]);
          pipCameraRef.current.srcObject = mediaStream;
          console.log('✅ Camera attached to PiP via MediaStream fallback in useEffect');
        } catch (fallbackErr) {
          console.error('❌ PiP camera fallback failed in useEffect:', fallbackErr);
        }
      }
    }
  }, [isScreenSharing, previewVideoTrack]);

  // Periodic check to ensure parallel streaming remains active
  useEffect(() => {
    let intervalId;
    if (isStreaming && livekitConnected) {
      intervalId = setInterval(() => {
        if (isScreenSharing) {
          ensureParallelStreaming();
        }
      }, 10000);
      console.log('🔄 Started periodic parallel streaming check');
    }
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
        console.log('🛑 Stopped periodic parallel streaming check');
      }
    };
  }, [isStreaming, livekitConnected, isScreenSharing]);

  // HTTP-based chat system
  useEffect(() => {
    if (isStreaming && sessionId) {
      console.log('💬 TEACHER: Starting HTTP-based chat for session:', sessionId);
      setSocketConnected(true);
      setStreamStatus('Chat system ready');
      loadChatHistory();
      const pollInterval = setInterval(() => {
        loadChatHistory();
      }, 2000);
      return () => {
        clearInterval(pollInterval);
        setSocketConnected(false);
      };
    }
  }, [isStreaming, sessionId]);

  // Cleanup transcription when component unmounts
  useEffect(() => {
    return () => {
      cleanupTranscriptionResources();
    };
  }, []);

  // Load chat history via Redux
  const loadChatHistory = async () => {
    if (!sessionId) return;
    try {
      dispatch(setChatLoading(true));
      dispatch(setChatError(null));
      const result = await getChatHistory(sessionId);
      if (result.data) {
        const newMessages = result.data;
        const currentMessageCount = chatMessages.length;
        if (newMessages.length > currentMessageCount) {
          const newCount = newMessages.length - currentMessageCount;
          if (newCount > 0 && !isChatOpen) {
            setUnreadMessages((prev) => prev + newCount);
          }
        }
        dispatch(setChatMessages(newMessages));
      } else if (result.error) {
        dispatch(setChatError(result.error.data || 'Failed to load chat history'));
      }
    } catch (error) {
      console.log('❌ TEACHER: Failed to load chat history:', error.message);
      dispatch(setChatError(error.message || 'Failed to load chat history'));
    } finally {
      dispatch(setChatLoading(false));
    }
  };

  const connectToLiveKitRoom = async () => {
    try {
      console.log('🔗 Connecting to Live room...');
      const room = new Room();
      room.on(RoomEvent.Connected, () => {
        console.log('✅ Connected to Live room');
        setLivekitConnected(true);
        setStreamStatus('Connected to Live room');
        setTimeout(() => {
          const publishedTracks = room.localParticipant.videoTracks;
          if (publishedTracks && publishedTracks.values) {
            console.log(
              '📹 Published video tracks:',
              Array.from(publishedTracks.values()).map((pub) => ({
                source: pub.source,
                name: pub.trackName,
                sid: pub.trackSid
              }))
            );
          } else {
            console.log('📹 Published video tracks: none available yet');
          }
        }, 1000);
      });

      room.on(RoomEvent.Disconnected, () => {
        console.log('❌ Disconnected from Live room');
        setLivekitConnected(false);
        setStreamStatus('Disconnected from Live room');
      });

      room.on(RoomEvent.ParticipantConnected, (participant) => {
        console.log('👤 Live Participant connected:', participant.identity, participant.name);
        setParticipants((prev) => [...prev, participant]);
        setJoinedViewers((prev) => {
          const existing = prev.find((v) => v.viewer_id === participant.identity);
          if (!existing) {
            return [
              ...prev,
              {
                viewer_id: participant.identity,
                viewer_name: participant.name || participant.identity,
                user_role: 'student',
                joined_at: new Date().toISOString(),
                source: 'livekit'
              }
            ];
          }
          return prev;
        });
      });

      room.on(RoomEvent.ParticipantDisconnected, (participant) => {
        console.log('👤 Live Participant disconnected:', participant.identity);
        setParticipants((prev) => prev.filter((p) => p.identity !== participant.identity));
        setJoinedViewers((prev) => prev.filter((v) => v.viewer_id !== participant.identity));
      });

      if (!livekitUrl) {
        throw new Error('LiveKit URL is not set');
      }

      await room.connect(livekitUrl, livekitToken);
      setLivekitRoom(room);
      livekitRoomRef.current = room;

      const cameraTrack = localVideoTrack || previewVideoTrack;
      if (cameraTrack) {
        try {
          await room.localParticipant.publishTrack(cameraTrack, {
            source: 'camera',
            name: 'teacher_camera'
          });
          console.log('✅ Camera track published to LiveKit on connect with camera source');
          if (!localVideoTrack && previewVideoTrack) {
            setLocalVideoTrack(previewVideoTrack);
          }
        } catch (cameraErr) {
          console.error('❌ Failed to publish camera track on connect:', cameraErr);
        }
      } else {
        console.log('⚠️ No camera track available to publish on connect');
      }

      try {
        // Use shared microphone stream for LiveKit audio
        if (sharedMicrophoneStreamRef.current) {
          console.log('🎤 Using shared microphone stream for LiveKit...');

          // Get the audio track from shared stream
          const sharedAudioTrack = sharedMicrophoneStreamRef.current.getAudioTracks()[0];
          console.log('🎤 Shared audio track:', sharedAudioTrack);

          if (sharedAudioTrack) {
            // Create LiveKit audio track from shared microphone track
            const audioTrack = await createLocalAudioTrack({
              deviceId: sharedAudioTrack.getSettings().deviceId,
              echoCancellation: true,
              noiseSuppression: true,
              autoGainControl: true
            });

            setLocalAudioTrack(audioTrack);
            await room.localParticipant.publishTrack(audioTrack, {
              name: 'teacher_microphone',
              source: 'microphone'
            });

            console.log('✅ Audio track published to LiveKit using shared microphone device');

            // Verify audio is working
            setTimeout(() => {
              const publishedTracks = room.localParticipant.audioTracks;
              console.log(
                '📊 Published audio tracks:',
                Array.from(publishedTracks.values()).map((pub) => ({
                  name: pub.trackName,
                  enabled: pub.track?.enabled,
                  muted: pub.track?.isMuted
                }))
              );
            }, 1000);
          } else {
            throw new Error('No audio track found in shared microphone stream');
          }
        } else {
          console.log('⚠️ No shared microphone stream, creating new audio track...');
          // Fallback to creating new audio track
          const audioTrack = await createLocalAudioTrack();
          setLocalAudioTrack(audioTrack);
          await room.localParticipant.publishTrack(audioTrack);
          console.log('✅ Audio track published to LiveKit (fallback)');
        }
      } catch (audioErr) {
        console.error('❌ Could not create audio track:', audioErr);

        // Try alternative approach - publish raw audio track
        try {
          if (sharedMicrophoneStreamRef.current) {
            console.log('🔄 Trying alternative approach - publishing raw audio track...');
            const sharedAudioTrack = sharedMicrophoneStreamRef.current.getAudioTracks()[0];
            if (sharedAudioTrack) {
              await room.localParticipant.publishTrack(sharedAudioTrack, {
                name: 'teacher_microphone_raw',
                source: 'microphone'
              });
              console.log('✅ Raw audio track published to LiveKit');
            }
          }
        } catch (fallbackErr) {
          console.error('❌ Fallback audio publishing also failed:', fallbackErr);
        }
      }
    } catch (err) {
      console.error('❌ Failed to connect to Live room:', err);
      setStreamStatus('Failed to connect to Live room');
      dispatch(setError(err.message || 'Failed to connect to Live room'));
    }
  };

  const initializeSharedMicrophone = async () => {
    try {
      console.log('🎤 Initializing shared microphone stream...');

      // Create shared microphone stream with specific constraints
      const microphoneStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100,
          // Ensure we get the default microphone device
          deviceId: 'default'
        }
      });

      sharedMicrophoneStreamRef.current = microphoneStream;
      console.log('✅ Shared microphone stream created');

      // Log microphone device info
      const audioTrack = microphoneStream.getAudioTracks()[0];
      if (audioTrack) {
        console.log('🎤 Microphone device:', audioTrack.label);
        console.log('🎤 Microphone settings:', audioTrack.getSettings());
      }

      // Add audio level monitoring for debugging
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const source = audioContext.createMediaStreamSource(microphoneStream);
      const analyser = audioContext.createAnalyser();
      source.connect(analyser);

      const dataArray = new Uint8Array(analyser.frequencyBinCount);
      const checkAudioLevel = () => {
        analyser.getByteFrequencyData(dataArray);
        const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
        if (average > 10) {
          // Audio detected
          console.log('🎤 Audio level detected:', average);
          setMicrophoneActive(true);
          // Reset microphone active state after 1 second of no audio
          setTimeout(() => setMicrophoneActive(false), 1000);
        }
      };

      // Check audio levels every 2 seconds for debugging
      const audioLevelInterval = setInterval(checkAudioLevel, 2000);

      // Store interval for cleanup
      microphoneStream.audioLevelInterval = audioLevelInterval;
      microphoneStream.audioContext = audioContext;

      return microphoneStream;
    } catch (err) {
      console.error('❌ Failed to initialize shared microphone:', err);
      throw err;
    }
  };

  const initializeCameraPreview = async () => {
    try {
      console.log('🎥 Initializing camera preview...');

      // Initialize shared microphone first
      await initializeSharedMicrophone();

      const videoTrack = await createLocalVideoTrack({
        resolution: { width: 640, height: 480 },
        frameRate: 15
      });
      console.log('✅ Video track created:', videoTrack);
      setPreviewVideoTrack(videoTrack);
      setCameraPermissionGranted(true);
      if (videoRef.current) {
        videoTrack.attach(videoRef.current);
        console.log('✅ Preview video track attached');
      } else {
        console.log('⚠️ videoRef not ready yet, will attach via useEffect');
      }
    } catch (err) {
      console.error('❌ Error initializing camera preview:', err);
      setCameraPermissionGranted(false);
      let errorMessage = 'Failed to initialize camera preview';
      if (err.name === 'NotAllowedError') {
        errorMessage = 'Camera permission denied. Please allow camera access.';
      } else if (err.name === 'NotFoundError') {
        errorMessage = 'No camera found. Please connect a camera.';
      } else if (err.name === 'NotReadableError') {
        errorMessage = 'Camera in use by another application.';
      } else if (err.message) {
        errorMessage = err.message;
      }
      setStreamStatus(errorMessage);
      dispatch(setError(errorMessage));
    }
  };

  const refreshCameraTrack = async () => {
    try {
      console.log('🔄 Refreshing camera track...');
      if (previewVideoTrack) {
        previewVideoTrack.stop();
        previewVideoTrack.detach();
      }
      const newVideoTrack = await createLocalVideoTrack({
        resolution: { width: 640, height: 480 },
        frameRate: 15
      });
      console.log('✅ New camera track created:', newVideoTrack);
      setPreviewVideoTrack(newVideoTrack);
      if (isScreenSharing && pipCameraRef.current) {
        newVideoTrack.attach(pipCameraRef.current);
        console.log('✅ New camera track attached to PiP');
      } else if (!isScreenSharing && videoRef.current) {
        newVideoTrack.attach(videoRef.current);
        console.log('✅ New camera track attached to main video');
      }
      if (localVideoTrack === previewVideoTrack) {
        setLocalVideoTrack(newVideoTrack);
      }
      if (livekitRoom && livekitConnected) {
        try {
          await livekitRoom.localParticipant.publishTrack(newVideoTrack, {
            source: 'camera',
            name: 'teacher_camera'
          });
          console.log('✅ New camera track published to LiveKit');
        } catch (publishErr) {
          console.warn('⚠️ Failed to publish new camera track:', publishErr);
        }
      }
      return newVideoTrack;
    } catch (err) {
      console.error('❌ Failed to refresh camera track:', err);
      throw err;
    }
  };

  const ensureParallelStreaming = async () => {
    try {
      console.log('🔄 Ensuring parallel streaming of camera and screen...');
      if (!livekitRoom || !livekitConnected) {
        console.warn('⚠️ Live room not connected');
        return;
      }
      const videoTracks = livekitRoom.localParticipant.videoTracks;
      if (!videoTracks || !videoTracks.values) {
        console.warn('⚠️ Video tracks not available yet');
        return;
      }
      const publishedTracks = Array.from(videoTracks.values());
      console.log(
        '📹 Currently published video tracks:',
        publishedTracks.map((pub) => ({
          source: pub.source,
          name: pub.trackName,
          sid: pub.trackSid
        }))
      );
      const cameraTrack = localVideoTrack || previewVideoTrack;
      const hasCameraTrack = publishedTracks.some(
        (pub) => pub.source === 'camera' || pub.trackName === 'teacher_camera'
      );
      if (cameraTrack && !hasCameraTrack) {
        await livekitRoom.localParticipant.publishTrack(cameraTrack, {
          source: 'camera',
          name: 'teacher_camera'
        });
        console.log('✅ Camera track ensured for parallel streaming');
      }
      if (isScreenSharing && localScreenTrack) {
        const hasScreenTrack = publishedTracks.some(
          (pub) => pub.source === 'screen_share' || pub.trackName === 'teacher_screen'
        );
        if (!hasScreenTrack) {
          await livekitRoom.localParticipant.publishTrack(localScreenTrack, {
            source: 'screen_share',
            name: 'teacher_screen'
          });
          console.log('✅ Screen track ensured for parallel streaming');
        }
      }
      console.log('✅ Parallel streaming ensured - both camera and screen tracks active');
    } catch (err) {
      console.error('❌ Failed to ensure parallel streaming:', err);
    }
  };

  // Chat functions
  const sendChatMessage = async () => {
    if (!newMessage.trim() || !socketConnected || !sessionId) return;
    const messageData = {
      session_id: sessionId,
      message: newMessage.trim(),
      sender_id: sessionStorage.getItem('userId'),
      sender_name: sessionStorage.getItem('name') || 'Teacher'
    };
    try {
      dispatch(setChatLoading(true));
      dispatch(setChatError(null));
      const result = await sendChatMessageMutation(messageData);
      if (result.data) {
        console.log('✅ TEACHER: Message sent successfully');
        setNewMessage('');
        setTimeout(loadChatHistory, 500);
      } else if (result.error) {
        console.error('❌ TEACHER: Failed to send message:', result.error);
        dispatch(setChatError(result.error.data || 'Failed to send message'));
      }
    } catch (error) {
      console.error('❌ TEACHER: Error sending message:', error);
      dispatch(setChatError(error.message || 'Failed to send message'));
    } finally {
      dispatch(setChatLoading(false));
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendChatMessage();
    }
  };

  const toggleChat = () => {
    setIsChatOpen(!isChatOpen);
    if (!isChatOpen) {
      setUnreadMessages(0);
    }
  };

  const formatMessageTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Quiz functions
  const handleGenerateQuiz = () => {
    setShowQuizUpload(true);
  };

  const handleQuizFileChange = (e) => {
    const file = e.target.files[0];
    if (file && file.type === 'application/pdf') {
      setQuizFile(file);
    } else {
      alert('Please select a PDF file');
    }
  };

  const handleUploadQuiz = async () => {
    if (!quizFile || !sessionId) {
      alert("Please select a PDF file and ensure you're streaming");
      return;
    }

    setIsUploadingQuiz(true);
    try {
      const formData = new FormData();
      formData.append('file', quizFile);

      const response = await uploadCbtPaper(formData).unwrap();

      if (response.object_id) {
        // Send quiz notification to all participants via chat
        const quizMessage = {
          session_id: sessionId,
          message: `🎯 QUIZ_START:${response.object_id} Quiz Started! Click 'Open Quiz' to participate. Total Questions: ${response.questions?.length || 0}`,
          sender_id: sessionStorage.getItem('userId'),
          sender_name: sessionStorage.getItem('name') || 'Teacher'
        };

        const result = await sendChatMessageMutation(quizMessage);
        console.log('Quiz message sent result:', result);

        if (result.data) {
          console.log('✅ Quiz message sent successfully');
          toast.success(
            `🎯 Quiz generated successfully! Quiz ID: ${response.object_id}\nQuiz message sent to all participants.`,
            {
              position: 'top-right',
              autoClose: 5000,
              hideProgressBar: false,
              closeOnClick: true,
              pauseOnHover: true,
              draggable: true
            }
          );
        } else if (result.error) {
          console.error('❌ Failed to send quiz message:', result.error);
          toast.warning(
            `⚠️ Quiz generated successfully! Quiz ID: ${response.object_id}\nWarning: Failed to notify participants via chat.`,
            {
              position: 'top-right',
              autoClose: 6000,
              hideProgressBar: false,
              closeOnClick: true,
              pauseOnHover: true,
              draggable: true
            }
          );
        }

        setShowQuizUpload(false);
        setQuizFile(null);

        // Refresh chat to show the quiz message
        setTimeout(loadChatHistory, 500);
      }
    } catch (error) {
      console.error('Quiz upload error:', error);
      toast.error(
        `❌ Failed to upload quiz: ${error.data?.message || error.message || 'Unknown error'}`,
        {
          position: 'top-right',
          autoClose: 5000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true
        }
      );
    } finally {
      setIsUploadingQuiz(false);
    }
  };

  // Test function to send a test quiz message - COMMENTED OUT FOR PRODUCTION
  // const sendTestQuizMessage = async () => {
  //   if (!sessionId) {
  //     alert("Please start streaming first")
  //     return
  //   }

  //   const testQuizMessage = {
  //     session_id: sessionId,
  //     message: `🎯 QUIZ_START:test123456789 Test Quiz Started! Click 'Open Quiz' to participate. Total Questions: 5`,
  //     sender_id: sessionStorage.getItem("userId"),
  //     sender_name: sessionStorage.getItem("name") || "Teacher",
  //   }

  //   try {
  //     const result = await sendChatMessageMutation(testQuizMessage)
  //     console.log("Test quiz message sent result:", result)
  //     if (result.data) {
  //       alert("Test quiz message sent successfully!")
  //     } else {
  //       alert("Failed to send test quiz message")
  //     }
  //     setTimeout(loadChatHistory, 500)
  //   } catch (error) {
  //     console.error("Error sending test quiz message:", error)
  //     alert("Error sending test quiz message")
  //   }
  // }

  const getRoleColor = (role) => {
    switch (role) {
      case 'kota_teacher':
      case 'faculty':
        return 'text-emerald-600';
      case 'student':
        return 'text-blue-600';
      case 'center_counselor':
        return 'text-purple-600';
      default:
        return 'text-gray-600';
    }
  };

  const getRoleBadge = (role) => {
    switch (role) {
      case 'kota_teacher':
      case 'faculty':
        return 'Teacher';
      case 'student':
        return 'Student';
      case 'center_counselor':
        return 'Counselor';
      default:
        return 'User';
    }
  };

  // Transcription Functions
  const startLiveTranscription = async () => {
    try {
      console.log('🎤 Starting live transcription...');
      setTranscriptionStatus('🎤 Initializing transcription...');

      // Check if already enabled
      if (isTranscriptionEnabled) {
        console.log('🎤 Transcription already enabled');
        return;
      }

      // Check browser support
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      if (!SpeechRecognition) {
        throw new Error('Speech recognition not supported. Please use Chrome or Edge browser.');
      }

      // CRITICAL: Request microphone permission explicitly
      console.log('🎤 Requesting microphone permission...');
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false
          }
        });
        console.log('🎤 ✅ Microphone permission granted');

        // Test audio levels to ensure mic is working
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const source = audioContext.createMediaStreamSource(stream);
        const analyser = audioContext.createAnalyser();
        source.connect(analyser);

        const dataArray = new Uint8Array(analyser.frequencyBinCount);
        analyser.getByteFrequencyData(dataArray);
        const audioLevel = dataArray.reduce((a, b) => a + b) / dataArray.length;

        console.log('🎤 Current audio level:', audioLevel);

        // Clean up test stream
        stream.getTracks().forEach((track) => track.stop());
        audioContext.close();

        if (audioLevel === 0) {
          console.warn('🎤 Warning: No audio detected from microphone');
        }
      } catch (micError) {
        console.error('🎤 ❌ Microphone access failed:', micError);
        throw new Error(`Microphone access failed: ${micError.message}`);
      }

      // Wait for microphone to be fully released
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Ensure shared microphone is available (for LiveKit)
      if (!sharedMicrophoneStreamRef.current) {
        console.log('🎤 Shared microphone not available, initializing...');
        await initializeSharedMicrophone();
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }

      // Initialize speech recognition for transcription
      setTranscriptionStatus('🎤 Starting speech recognition...');
      await initializeSpeechRecognition();

      setIsTranscriptionEnabled(true);
      console.log('🎤 ✅ Live transcription started successfully');
    } catch (error) {
      setTranscriptionStatus(`❌ Error: ${error.message}`);
      console.error('❌ Transcription start error:', error);

      // Show user-friendly error message
      if (error.message.includes('not supported')) {
        setTranscriptionStatus('❌ Speech recognition not supported. Use Chrome/Edge browser.');
      } else if (
        error.message.includes('permission') ||
        error.message.includes('NotAllowedError')
      ) {
        setTranscriptionStatus('❌ Microphone permission required. Please allow access.');
      } else if (error.message.includes('NotFoundError')) {
        setTranscriptionStatus('❌ No microphone found. Please connect a microphone.');
      } else {
        setTranscriptionStatus(`❌ Error: ${error.message}`);
      }
    }
  };

  const stopLiveTranscription = async () => {
    try {
      console.log('🎤 Stopping live transcription...');
      setTranscriptionStatus('Stopping transcription...');

      // Stop speech recognition
      if (speechRecognition) {
        console.log('🎤 Stopping speech recognition...');
        speechRecognition.stop();
        setSpeechRecognition(null);
      }

      // Close LiveKit text stream
      if (livekitTextStream) {
        console.log('📝 Closing LiveKit text stream...');
        await livekitTextStream.close();
        setLivekitTextStream(null);
      }

      setIsTranscriptionEnabled(false);
      setCurrentTranscription('');
      setTranscriptionStatus('');
      setMicrophoneActive(false);

      console.log('🎤 Live transcription stopped successfully');
    } catch (error) {
      setTranscriptionStatus(`Error: ${error.message}`);
      console.error('❌ Transcription stop error:', error);
    }
  };

  // Send transcribed text via LiveKit text stream
  const sendTranscriptionViaLiveKit = async (text) => {
    try {
      if (!livekitRoom || !livekitConnected) {
        console.warn('Live room not connected, cannot send transcription');
        return;
      }
      // Use sendText for each transcript (LiveKit best practice)
      await livekitRoom.localParticipant.sendText(text, { topic: 'transcription' });
      console.log(`📝 ✅ Sent transcription via LiveKit sendText: ${text}`);
    } catch (error) {
      console.error('❌ Failed to send transcription via LiveKit:', error);
      setTranscriptionStatus(`Error sending transcription: ${error.message}`);
    }
  };

  const initializeSpeechRecognition = async () => {
    try {
      console.log('🎤 Initializing speech recognition...');

      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      if (!SpeechRecognition) {
        throw new Error('Speech recognition not supported in this browser');
      }

      // CRITICAL: Stop any existing recognition first
      if (speechRecognition) {
        console.log('🎤 Stopping existing speech recognition...');
        try {
          speechRecognition.stop();
        } catch (e) {
          console.log('🎤 No existing recognition to stop');
        }
        setSpeechRecognition(null);
        await new Promise((resolve) => setTimeout(resolve, 500));
      }

      // Force microphone access to ensure it's available
      console.log('🎤 Requesting fresh microphone access...');
      try {
        const micStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false,
            sampleRate: 16000
          }
        });
        console.log('🎤 Fresh microphone access granted');

        // Keep the stream active for a moment to ensure mic is ready
        setTimeout(() => {
          micStream.getTracks().forEach((track) => track.stop());
          console.log('🎤 Test microphone stream stopped');
        }, 1000);
      } catch (micError) {
        console.error('🎤 Microphone access failed:', micError);
        throw new Error(`Microphone access failed: ${micError.message}`);
      }

      // Wait a moment for microphone to be fully available
      await new Promise((resolve) => setTimeout(resolve, 1500));

      const recognition = new SpeechRecognition();

      // CRITICAL SETTINGS for better recognition
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.maxAlternatives = 1;

      // Use the selected language but fallback to English
      const langMap = {
        en: 'en-US',
        ta: 'ta-IN',
        hi: 'hi-IN',
        te: 'te-IN',
        kn: 'kn-IN'
      };
      recognition.lang = langMap[sourceLanguage] || 'en-US';

      console.log('🎤 Speech recognition configured with language:', recognition.lang);

      let restartTimeout = null;
      let isRestarting = false;

      recognition.onstart = () => {
        console.log('🎤 ✅ Speech recognition STARTED successfully');
        setTranscriptionStatus('🎤 Listening for speech...');
        isRestarting = false;
      };

      recognition.onresult = (event) => {
        console.log('🎤 ✅ SPEECH DETECTED - Processing results...');
        const result = event.results[event.results.length - 1];
        const transcript = result[0].transcript;
        const confidence = result[0].confidence;

        console.log(
          '🎤 Transcript:',
          transcript,
          'Confidence:',
          confidence,
          'isFinal:',
          result.isFinal
        );

        // Update current transcription for display
        setCurrentTranscription(transcript);
        setTranscriptionStatus('🎤 Speech recognized - transcribing...');

        // Send final results via LiveKit text stream
        if (result.isFinal && transcript.trim()) {
          console.log('🎤 Sending final transcript:', transcript.trim());
          sendTranscriptionViaLiveKit(transcript.trim());
        }
      };

      recognition.onaudiostart = () => {
        console.log('🎤 ✅ Audio input STARTED');
        setMicrophoneActive(true);
        setTranscriptionStatus('🎤 Audio input active - speak now...');
      };

      recognition.onaudioend = () => {
        console.log('🎤 Audio input ended');
        setMicrophoneActive(false);
      };

      recognition.onspeechstart = () => {
        console.log('🎤 ✅ SPEECH DETECTED!');
        setTranscriptionStatus('🎤 Speech detected - processing...');
      };

      recognition.onspeechend = () => {
        console.log('🎤 Speech ended');
        setTranscriptionStatus('🎤 Speech ended - waiting for more...');
      };

      recognition.onnomatch = () => {
        console.log('🎤 No speech match found');
        setTranscriptionStatus('🎤 No clear speech - please speak clearly');
      };

      recognition.onerror = (event) => {
        console.error('🎤 ❌ Speech recognition error:', event.error);

        if (event.error === 'no-speech') {
          console.log('🎤 No speech detected - will restart immediately...');
          setTranscriptionStatus('🎤 No speech detected - restarting...');

          // AGGRESSIVE RESTART for no-speech - this is the key fix
          if (isTranscriptionEnabled && !isRestarting) {
            isRestarting = true;
            // Don't wait, restart immediately
            restartTimeout = setTimeout(() => {
              try {
                console.log('🎤 IMMEDIATE restart after no-speech...');
                if (speechRecognition === recognition && isTranscriptionEnabled) {
                  recognition.start();
                }
              } catch (error) {
                console.error('Failed to restart after no-speech:', error);
                // If restart fails, try creating a new recognition instance
                if (isTranscriptionEnabled) {
                  console.log('🎤 Creating new recognition instance...');
                  initializeSpeechRecognition();
                }
                isRestarting = false;
              }
            }, 100); // VERY fast restart - 100ms only
          }
        } else if (event.error === 'audio-capture') {
          setTranscriptionStatus('❌ Microphone access error - check permissions');
          // Try to reinitialize microphone access
          if (isTranscriptionEnabled) {
            console.log('🎤 Attempting to reinitialize microphone...');
            setTimeout(() => {
              initializeSpeechRecognition();
            }, 2000);
          }
        } else if (event.error === 'not-allowed') {
          setTranscriptionStatus('❌ Microphone permission denied');
        } else if (event.error === 'network') {
          setTranscriptionStatus('❌ Network error - check connection');
        } else if (event.error === 'aborted') {
          console.log('🎤 Recognition aborted - normal during restart');
        } else {
          setTranscriptionStatus(`❌ Error: ${event.error}`);
          // For any other error, try to restart
          if (isTranscriptionEnabled && !isRestarting) {
            console.log('🎤 Unknown error, attempting restart...');
            setTimeout(() => {
              initializeSpeechRecognition();
            }, 1000);
          }
        }
      };

      recognition.onend = () => {
        console.log('🎤 Speech recognition ended - will restart immediately');

        // Clear any pending restart
        if (restartTimeout) {
          clearTimeout(restartTimeout);
          restartTimeout = null;
        }

        // AGGRESSIVE AUTO-RESTART - this is critical for continuous operation
        if (isTranscriptionEnabled && !isRestarting) {
          isRestarting = true;
          // Immediate restart with minimal delay
          restartTimeout = setTimeout(() => {
            try {
              console.log('🎤 IMMEDIATE auto-restart...');
              if (speechRecognition === recognition && isTranscriptionEnabled) {
                recognition.start();
              } else {
                console.log('🎤 Recognition instance changed, creating new one...');
                initializeSpeechRecognition();
              }
            } catch (error) {
              console.error('Failed to auto-restart:', error);
              if (error.name === 'InvalidStateError') {
                console.log('🎤 Recognition already running, ignoring error');
                isRestarting = false;
              } else {
                // If restart fails, create a completely new instance
                console.log('🎤 Creating fresh recognition instance...');
                setTimeout(() => {
                  initializeSpeechRecognition();
                }, 500);
              }
            }
          }, 50); // VERY fast restart - 50ms only
        }
      };

      setSpeechRecognition(recognition);

      // Start recognition
      console.log('🎤 Starting speech recognition...');
      recognition.start();
      setTranscriptionStatus('🎤 Starting speech recognition...');
    } catch (error) {
      setTranscriptionStatus(`❌ Error: ${error.message}`);
      console.error('❌ Speech recognition initialization error:', error);
    }
  };

  const startStreaming = async () => {
    try {
      if (!previewVideoTrack && !localVideoTrack) {
        console.log('🎥 Camera not initialized, initializing now...');
        await initializeCameraPreview();
        await new Promise((resolve) => setTimeout(resolve, 500));
      }

      // Ensure shared microphone is available before starting
      if (!sharedMicrophoneStreamRef.current) {
        console.log('🎤 Shared microphone not initialized, initializing now...');
        await initializeSharedMicrophone();
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
      const userId = sessionStorage.getItem('userId');
      const response = await startStream({
        userId,
        sessionId,
        quality,
        screenShareEnabled: isScreenSharing
      }).unwrap();
      const backendSessionId = response.session_id;
      if (backendSessionId && backendSessionId !== sessionId) {
        setSessionId(backendSessionId);
      }

      // Store the stream session ID in session storage for stopping later
      if (backendSessionId) {
        sessionStorage.setItem('streamSessionId', backendSessionId);
        console.log('✅ Stream session ID stored in session storage:', backendSessionId);
      }

      dispatch(
        setStreamingData({ streamToken: response.livekit_token, roomName: response.roomName })
      );
      dispatch(setIsStreaming(true));
      setLivekitToken(response.livekit_token);
      setLivekitUrl(response.livekit_url);
      if (previewVideoTrack) {
        setLocalVideoTrack(previewVideoTrack);
      }
      setStreamStatus('LiveKit stream started successfully');
    } catch (err) {
      console.error('❌ Failed to start stream:', err);
      setStreamStatus('Failed to start stream');
      dispatch(setError(err.message || 'Failed to start stream'));
    }
  };

  const stopStreaming = async () => {
    try {
      // Get the stored stream session ID from session storage
      const storedSessionId = sessionStorage.getItem('streamSessionId');
      const sessionIdToUse = storedSessionId || sessionId;

      console.log('🛑 Stopping stream with session ID:', sessionIdToUse);

      await stopStream({ session_id: sessionIdToUse }).unwrap();

      // Remove the stored session ID from session storage
      sessionStorage.removeItem('streamSessionId');
      console.log('✅ Stream session ID removed from session storage');

      cleanupAllResources();
      dispatch(setIsStreaming(false));
      dispatch(clearStreamingData());
      setStreamStatus('Ready to start streaming');
    } catch (err) {
      console.error('❌ Failed to stop stream:', err);
      dispatch(setError(err.message || 'Failed to stop stream'));
    }
  };

  const cleanupLiveKitResources = () => {
    if (localVideoTrack) {
      localVideoTrack.stop();
      localVideoTrack.detach();
      setLocalVideoTrack(null);
    }
    if (localAudioTrack) {
      localAudioTrack.stop();
      localAudioTrack.detach();
      setLocalAudioTrack(null);
    }
    if (previewVideoTrack) {
      previewVideoTrack.stop();
      previewVideoTrack.detach();
      setPreviewVideoTrack(null);
    }
    if (localScreenTrack) {
      localScreenTrack.stop();
      localScreenTrack.detach();
      setLocalScreenTrack(null);
    }
    if (localScreenAudioTrack) {
      localScreenAudioTrack.stop();
      localScreenAudioTrack.detach();
      setLocalScreenAudioTrack(null);
    }
    if (screenStream) {
      const tracks = screenStream.getTracks();
      tracks.forEach((track) => track.stop());
      setScreenStream(null);
    }
    if (livekitRoom) {
      livekitRoom.disconnect();
      setLivekitRoom(null);
      setLivekitConnected(false);
    }
    livekitRoomRef.current = null;
    console.log('🧹 LiveKit resources cleaned up');
  };

  const cleanupAllResources = () => {
    cleanupLiveKitResources();
    cleanupTranscriptionResources();
    cleanupSharedMicrophone();
    setIsScreenSharing(false);
    setParticipants([]);
  };

  const cleanupTranscriptionResources = () => {
    // Stop speech recognition
    if (speechRecognition) {
      if (speechRecognition.stop) {
        // Web Speech Recognition API
        speechRecognition.stop();
      }
      setSpeechRecognition(null);
    }

    // Close LiveKit text stream
    if (livekitTextStream) {
      livekitTextStream.close().catch(console.error);
      setLivekitTextStream(null);
    }

    // Reset transcription states
    setIsTranscriptionEnabled(false);
    setCurrentTranscription('');
    setTranscriptionStatus('');
  };

  const cleanupSharedMicrophone = () => {
    // Clean up shared microphone stream
    if (sharedMicrophoneStreamRef.current) {
      // Clean up audio monitoring
      if (sharedMicrophoneStreamRef.current.audioLevelInterval) {
        clearInterval(sharedMicrophoneStreamRef.current.audioLevelInterval);
      }
      if (sharedMicrophoneStreamRef.current.audioContext) {
        sharedMicrophoneStreamRef.current.audioContext.close();
      }

      // Stop all tracks
      sharedMicrophoneStreamRef.current.getTracks().forEach((track) => {
        track.stop();
      });
      sharedMicrophoneStreamRef.current = null;
      console.log('🧹 Shared microphone stream cleaned up');
    }
  };

  const startScreenShare = async () => {
    try {
      console.log('🖥️ Starting screen share...');
      setStreamStatus('Starting screen share...');
      const screenMediaStream = await navigator.mediaDevices.getDisplayMedia({
        video: { width: { ideal: 1920 }, height: { ideal: 1080 }, frameRate: { ideal: 30 } },
        audio: true
      });
      setScreenStream(screenMediaStream);
      const screenTracks = await createLocalScreenTracks({ audio: true, video: true });
      const screenVideoTrack = screenTracks.find((track) => track.kind === 'video');
      const screenAudioTrack = screenTracks.find((track) => track.kind === 'audio');
      if (screenVideoTrack) {
        setLocalScreenTrack(screenVideoTrack);
        if (screenAudioTrack) setLocalScreenAudioTrack(screenAudioTrack);
        screenVideoTrack.attach(screenVideoRef.current);
        const videoTrack = screenMediaStream.getVideoTracks()[0];
        videoTrack?.addEventListener('ended', () => stopScreenShare());
        setIsScreenSharing(true);
        setStreamStatus('Screen sharing active');
        setTimeout(ensureParallelStreaming, 1000);
        if (previewVideoTrack && pipCameraRef.current) {
          previewVideoTrack.detach(videoRef.current);
          previewVideoTrack.attach(pipCameraRef.current);
        }
        if (livekitRoom && livekitConnected) {
          await livekitRoom.localParticipant.publishTrack(screenVideoTrack, {
            source: 'screen_share'
          });
          if (screenAudioTrack)
            await livekitRoom.localParticipant.publishTrack(screenAudioTrack, {
              source: 'screen_share_audio'
            });
          const cameraTrack = localVideoTrack || previewVideoTrack;
          if (cameraTrack) {
            const hasCamera = Array.from(livekitRoom.localParticipant.videoTracks.values()).some(
              (pub) => pub.source === 'camera'
            );
            if (!hasCamera)
              await livekitRoom.localParticipant.publishTrack(cameraTrack, { source: 'camera' });
          }
        }
      }
    } catch (err) {
      console.error('❌ Error starting screen share:', err);
      let errorMessage = 'Failed to start screen share';
      if (err.name === 'NotAllowedError')
        errorMessage = 'Screen share permission denied. Please allow screen sharing.';
      setStreamStatus(errorMessage);
      dispatch(setError(errorMessage));
      setIsScreenSharing(false);
    }
  };

  const stopScreenShare = async () => {
    try {
      console.log('🛑 Stopping screen share...');
      setStreamStatus('Stopping screen share...');
      if (livekitRoom && livekitConnected) {
        if (localScreenTrack) await livekitRoom.localParticipant.unpublishTrack(localScreenTrack);
        if (localScreenAudioTrack)
          await livekitRoom.localParticipant.unpublishTrack(localScreenAudioTrack);
      }
      screenStream?.getTracks().forEach((track) => track.stop());
      setScreenStream(null);
      localScreenTrack?.stop();
      localScreenTrack?.detach();
      setLocalScreenTrack(null);
      localScreenAudioTrack?.stop();
      localScreenAudioTrack?.detach();
      setLocalScreenAudioTrack(null);
      setIsScreenSharing(false);
      setStreamStatus('Screen sharing stopped');
      if (previewVideoTrack && videoRef.current) {
        previewVideoTrack.detach(pipCameraRef.current);
        previewVideoTrack.attach(videoRef.current);
      }
    } catch (err) {
      console.error('❌ Error stopping screen share:', err);
      setStreamStatus('Error stopping screen share');
      dispatch(setError(err.message || 'Failed to stop screen share'));
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          animate={{
            x: [0, 100, 0],
            y: [0, -100, 0]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: 'linear'
          }}
          className="absolute top-10 left-10 w-32 h-32 bg-blue-200/30 rounded-full blur-xl"
        />
        <motion.div
          animate={{
            x: [0, -150, 0],
            y: [0, 100, 0]
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: 'linear'
          }}
          className="absolute top-1/2 right-20 w-40 h-40 bg-indigo-200/20 rounded-full blur-xl"
        />
        <motion.div
          animate={{
            x: [0, 80, 0],
            y: [0, -80, 0]
          }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: 'linear'
          }}
          className="absolute bottom-20 left-1/3 w-24 h-24 bg-purple-200/25 rounded-full blur-xl"
        />
      </div>

      <div className="max-w-8xl mx-auto p-4 sm:p-6 relative z-10">
        {/* Enhanced Header */}
        <motion.div
          initial={{ opacity: 0, y: -30, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.6, ease: 'easeOut' }}
          className="bg-white/90 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/30 p-8 mb-8 relative overflow-hidden"
        >
          {/* Header background gradient */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-50/50 via-indigo-50/30 to-purple-50/50 rounded-3xl" />

          <div className="relative z-10 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6">
            <div className="flex items-center space-x-4">
              <motion.div
                whileHover={{ scale: 1.1, rotate: 5 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                className="w-16 h-16 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg"
              >
                <motion.svg
                  className="w-8 h-8 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  whileHover={{ scale: 1.1 }}
                  transition={{ duration: 0.2 }}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </motion.svg>
              </motion.div>
              <div>
                <motion.h1
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2, duration: 0.5 }}
                  className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent"
                >
                  Live Streaming Studio
                </motion.h1>
                <motion.p
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3, duration: 0.5 }}
                  className="text-gray-600 text-base font-medium mt-1"
                >
                  Professional Teaching Platform ✨
                </motion.p>
              </div>
            </div>
            {isStreaming && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4, duration: 0.5 }}
                className="flex flex-col sm:flex-row items-start sm:items-center gap-4"
              >
                <motion.select
                  whileHover={{ scale: 1.02 }}
                  whileFocus={{ scale: 1.02 }}
                  value={quality}
                  onChange={(e) => setQuality(e.target.value)}
                  className="px-5 py-3 bg-white/80 border border-gray-200/50 rounded-2xl focus:outline-none focus:ring-3 focus:ring-blue-500/30 focus:border-blue-400 transition-all duration-300 shadow-lg backdrop-blur-sm font-medium"
                >
                  <option value="low">🔹 Low Quality</option>
                  <option value="medium">🔸 Medium Quality</option>
                  <option value="high">🔶 High Quality</option>
                </motion.select>

                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                  onClick={handleGenerateQuiz}
                  className="px-8 py-3 bg-gradient-to-r from-purple-500 via-purple-600 to-indigo-600 text-white rounded-2xl hover:from-purple-600 hover:via-purple-700 hover:to-indigo-700 transition-all duration-300 shadow-xl hover:shadow-2xl font-semibold relative overflow-hidden group"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <span className="relative flex items-center space-x-3">
                    <motion.svg
                      className="w-5 h-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      whileHover={{ rotate: 15 }}
                      transition={{ duration: 0.2 }}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </motion.svg>
                    <span>Generate Quiz</span>
                  </span>
                </motion.button>

                {/* Test Quiz Button - HIDDEN FOR PRODUCTION */}
                {/* <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={sendTestQuizMessage}
                  className="px-4 py-2 bg-gradient-to-r from-yellow-500 to-yellow-600 text-white rounded-xl hover:from-yellow-600 hover:to-yellow-700 transition-all duration-200 shadow-lg hover:shadow-xl font-medium text-sm"
                >
                  <span className="flex items-center space-x-2">
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 10V3L4 14h7v7l9-11h-7z"
                      />
                    </svg>
                    <span>Test Quiz</span>
                  </span>
                </motion.button> */}

                <motion.button
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                  onClick={stopStreaming}
                  className="px-8 py-3 bg-gradient-to-r from-red-500 via-red-600 to-pink-600 text-white rounded-2xl hover:from-red-600 hover:via-red-700 hover:to-pink-700 transition-all duration-300 shadow-xl hover:shadow-2xl font-semibold relative overflow-hidden group"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  <span className="relative flex items-center space-x-3">
                    <motion.span
                      className="w-3 h-3 bg-white rounded-full"
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                    />
                    <span>Stop Streaming</span>
                  </span>
                </motion.button>
              </motion.div>
            )}
          </div>

          {/* Enhanced Transcription Controls */}
          {isStreaming && (
            <motion.div
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2, ease: 'easeOut' }}
              className="mt-6 p-6 bg-gradient-to-br from-purple-50/80 via-indigo-50/60 to-blue-50/80 backdrop-blur-sm border border-purple-200/50 rounded-3xl shadow-lg relative overflow-hidden"
            >
              {/* Background decoration */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-200/30 to-indigo-200/20 rounded-full blur-2xl transform translate-x-16 -translate-y-16" />

              <div className="relative z-10 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-6">
                  <div className="flex items-center space-x-3">
                    <motion.div
                      animate={microphoneActive ? { scale: [1, 1.1, 1] } : {}}
                      transition={{ duration: 0.5, repeat: microphoneActive ? Infinity : 0 }}
                      className={`p-2 rounded-xl ${microphoneActive ? 'bg-green-100' : 'bg-purple-100'} transition-colors duration-300`}
                    >
                      <svg
                        className={`w-6 h-6 ${microphoneActive ? 'text-green-600' : 'text-purple-600'} transition-colors duration-300`}
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
                        />
                      </svg>
                    </motion.div>
                    <div>
                      <span className="text-purple-800 font-semibold text-lg">
                        Live Transcription
                      </span>
                      {microphoneActive && (
                        <motion.div
                          initial={{ opacity: 0, y: 5 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="text-green-600 text-sm font-medium"
                        >
                          🎤 Listening...
                        </motion.div>
                      )}
                    </div>
                  </div>

                  <motion.select
                    whileHover={{ scale: 1.02 }}
                    whileFocus={{ scale: 1.02 }}
                    value={sourceLanguage}
                    onChange={(e) => setSourceLanguage(e.target.value)}
                    className="px-4 py-2 bg-white/80 border border-purple-300/50 rounded-xl text-sm focus:outline-none focus:ring-3 focus:ring-purple-500/30 focus:border-purple-400 transition-all duration-300 shadow-md backdrop-blur-sm font-medium"
                    disabled={isTranscriptionEnabled}
                  >
                    {availableSourceLanguages.map((lang) => (
                      <option key={lang.code} value={lang.code}>
                        {lang.flag} {lang.name}
                      </option>
                    ))}
                  </motion.select>

                  <motion.button
                    whileHover={{ scale: 1.05, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                    transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                    onClick={
                      isTranscriptionEnabled ? stopLiveTranscription : startLiveTranscription
                    }
                    className={`px-6 py-3 rounded-2xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl relative overflow-hidden group ${
                      isTranscriptionEnabled
                        ? 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white'
                        : 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white'
                    }`}
                  >
                    <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    <span className="relative flex items-center space-x-2">
                      {isTranscriptionEnabled ? (
                        <>
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
                            className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                          />
                          <span>Stop Transcription</span>
                        </>
                      ) : (
                        <>
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728"
                            />
                          </svg>
                          <span>Start Transcription</span>
                        </>
                      )}
                    </span>
                  </motion.button>

                  {/* Debug button for testing microphone - HIDDEN FOR PRODUCTION */}
                  {/* <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={async () => {
                      try {
                        console.log('🎤 Testing microphone and speech recognition...');
                        setTranscriptionStatus('🎤 Testing microphone...');

                        // Test microphone access
                        const stream = await navigator.mediaDevices.getUserMedia({
                          audio: {
                            echoCancellation: false,
                            noiseSuppression: false,
                            autoGainControl: false
                          }
                        });
                        console.log('🎤 Microphone access successful');

                        // Test audio levels
                        const audioContext = new (window.AudioContext ||
                          window.webkitAudioContext)();
                        const source = audioContext.createMediaStreamSource(stream);
                        const analyser = audioContext.createAnalyser();
                        source.connect(analyser);

                        const dataArray = new Uint8Array(analyser.frequencyBinCount);

                        let maxLevel = 0;
                        const checkAudio = () => {
                          analyser.getByteFrequencyData(dataArray);
                          const level = dataArray.reduce((a, b) => a + b) / dataArray.length;
                          maxLevel = Math.max(maxLevel, level);
                        };

                        // Check audio for 3 seconds
                        const interval = setInterval(checkAudio, 100);
                        setTimeout(() => {
                          clearInterval(interval);
                          stream.getTracks().forEach((track) => track.stop());
                          audioContext.close();

                          console.log('🎤 Max audio level detected:', maxLevel);
                          if (maxLevel > 5) {
                            setTranscriptionStatus(
                              `✅ Microphone working! Max level: ${maxLevel.toFixed(1)}`
                            );
                          } else {
                            setTranscriptionStatus(
                              `⚠️ Low audio level: ${maxLevel.toFixed(1)} - speak louder`
                            );
                          }
                        }, 3000);

                        setTranscriptionStatus('🎤 Speak now for 3 seconds to test...');
                      } catch (error) {
                        console.error('🎤 Microphone test failed:', error);
                        setTranscriptionStatus(`❌ Test failed: ${error.message}`);
                      }
                    }}
                    className="px-3 py-1 text-xs bg-gray-500 hover:bg-gray-600 text-white rounded-lg"
                  >
                    Test Mic
                  </motion.button> */}

                  {/* Force restart speech recognition */}
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={async () => {
                      try {
                        console.log('🎤 Force restarting speech recognition...');
                        setTranscriptionStatus('🎤 Force restarting...');

                        // Stop current recognition
                        if (speechRecognition) {
                          speechRecognition.stop();
                          setSpeechRecognition(null);
                        }

                        // Wait and restart
                        await new Promise((resolve) => setTimeout(resolve, 1000));
                        await initializeSpeechRecognition();
                      } catch (error) {
                        console.error('🎤 Force restart failed:', error);
                        setTranscriptionStatus(`❌ Restart failed: ${error.message}`);
                      }
                    }}
                    className="px-3 py-1 text-xs bg-orange-500 hover:bg-orange-600 text-white rounded-lg"
                    disabled={!isTranscriptionEnabled}
                  >
                    Force Restart
                  </motion.button>
                </div>

                {transcriptionStatus && (
                  <div className="text-sm text-purple-700">{transcriptionStatus}</div>
                )}
              </div>

              {/* Enhanced Live Transcription Display */}
              {isTranscriptionEnabled && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="mt-4 p-4 bg-white/90 backdrop-blur-sm border border-purple-300/50 rounded-2xl shadow-lg"
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 1.5, repeat: Infinity }}
                        className="w-3 h-3 bg-red-500 rounded-full"
                      />
                      <span className="text-sm font-bold text-purple-600 tracking-wide">
                        LIVE TRANSCRIPTION
                      </span>
                      <span className="px-2 py-1 bg-purple-100 text-purple-700 text-xs font-medium rounded-full">
                        {sourceLanguage.toUpperCase()}
                      </span>
                    </div>
                    {currentTranscription && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="text-xs text-green-600 font-medium bg-green-50 px-2 py-1 rounded-full"
                      >
                        ✓ Active
                      </motion.div>
                    )}
                  </div>
                  <div className="min-h-[80px] p-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border border-gray-200/50 relative overflow-hidden">
                    {currentTranscription ? (
                      <motion.p
                        key={currentTranscription}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="text-base text-gray-800 leading-relaxed font-medium"
                      >
                        {currentTranscription}
                      </motion.p>
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <motion.p
                          animate={{ opacity: [0.5, 1, 0.5] }}
                          transition={{ duration: 2, repeat: Infinity }}
                          className="text-sm text-gray-400 italic flex items-center space-x-2"
                        >
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
                            />
                          </svg>
                          <span>Waiting for speech...</span>
                        </motion.p>
                      </div>
                    )}
                  </div>
                </motion.div>
              )}
            </motion.div>
          )}

          <AnimatePresence>
            {isStreaming && (
              <motion.div
                initial={{ opacity: 0, y: 20, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: 20, scale: 0.95 }}
                transition={{ duration: 0.4, ease: 'easeOut' }}
                className="mt-6 p-6 bg-gradient-to-br from-emerald-50/90 via-green-50/80 to-teal-50/90 backdrop-blur-sm border border-emerald-200/50 rounded-3xl shadow-lg relative overflow-hidden"
              >
                {/* Background decoration */}
                <div className="absolute top-0 left-0 w-24 h-24 bg-gradient-to-br from-emerald-200/40 to-green-200/30 rounded-full blur-xl transform -translate-x-8 -translate-y-8" />

                <div className="relative z-10 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                  <div className="flex items-center space-x-4">
                    <motion.div
                      animate={{
                        scale: livekitConnected ? [1, 1.2, 1] : 1,
                        boxShadow: livekitConnected
                          ? [
                              '0 0 0 0 rgba(16, 185, 129, 0.4)',
                              '0 0 0 10px rgba(16, 185, 129, 0)',
                              '0 0 0 0 rgba(16, 185, 129, 0)'
                            ]
                          : '0 0 0 0 rgba(16, 185, 129, 0)'
                      }}
                      transition={{ duration: 1.5, repeat: Infinity }}
                      className="w-4 h-4 bg-emerald-500 rounded-full shadow-lg"
                    />
                    <span className="text-emerald-800 font-bold text-lg tracking-wide">
                      LIVE STREAMING
                    </span>

                    <div className="h-6 w-px bg-emerald-300/50"></div>

                    <div className="flex items-center space-x-3 bg-white/60 backdrop-blur-sm px-3 py-2 rounded-xl">
                      <motion.div
                        animate={{
                          scale: microphoneActive ? [1, 1.4, 1] : 1,
                          backgroundColor: microphoneActive ? '#ef4444' : '#9ca3af'
                        }}
                        transition={{ duration: 0.3 }}
                        className="w-3 h-3 rounded-full shadow-sm"
                      />
                      <span className="text-emerald-700 text-sm font-semibold">
                        {microphoneActive ? '🎤 RECORDING' : '🎤 STANDBY'}
                      </span>
                    </div>

                    {isTranscriptionEnabled && (
                      <>
                        <div className="h-6 w-px bg-emerald-300/50"></div>
                        <motion.div
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          className="flex items-center space-x-2 bg-purple-100/80 backdrop-blur-sm px-3 py-2 rounded-xl"
                        >
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
                            className="w-3 h-3 border-2 border-purple-500 border-t-transparent rounded-full"
                          />
                          <span className="text-purple-700 text-sm font-semibold">
                            TRANSCRIBING
                          </span>
                        </motion.div>
                      </>
                    )}
                  </div>

                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                    className="bg-white/70 backdrop-blur-sm px-4 py-2 rounded-xl shadow-sm"
                  >
                    <p className="text-emerald-700 text-sm font-medium">{streamStatus}</p>
                  </motion.div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {isStreaming ? (
          <div className="flex flex-col xl:flex-row gap-6 relative">
            {/* Main Video Area */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5 }}
              className="flex-grow"
            >
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 overflow-hidden relative">
                <div
                  className="bg-gradient-to-r from-gray-900 to-black"
                  style={{ aspectRatio: '16/9' }}
                >
                  <AnimatePresence mode="wait">
                    {!isScreenSharing ? (
                      <motion.div
                        key="camera-view"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="flex items-center justify-center h-full text-white p-8 relative"
                      >
                        <div className="text-center space-y-6">
                          <motion.div
                            initial={{ scale: 0.8 }}
                            animate={{ scale: 1 }}
                            className="w-20 h-20 bg-white/10 rounded-full flex items-center justify-center mx-auto"
                          >
                            <svg
                              className="w-10 h-10"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                              />
                            </svg>
                          </motion.div>
                          <h3 className="text-xl font-semibold">Ready to Share Your Screen</h3>
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            className="px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium"
                            onClick={startScreenShare}
                          >
                            Start Screen Share
                          </motion.button>
                        </div>

                        {/* Live Transcription Overlay - Camera View */}
                        {isTranscriptionEnabled && currentTranscription && (
                          <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: 20 }}
                            className="absolute bottom-6 left-6 right-6 bg-black/80 backdrop-blur-sm text-white p-4 rounded-lg border border-white/20"
                          >
                            <div className="flex items-center space-x-2 mb-2">
                              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                              <span className="text-xs font-medium text-gray-300">
                                LIVE TRANSCRIPTION
                              </span>
                            </div>
                            <p className="text-sm leading-relaxed">{currentTranscription}</p>
                          </motion.div>
                        )}
                      </motion.div>
                    ) : (
                      <motion.div
                        key="screen-share"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="relative w-full h-full"
                      >
                        <video
                          ref={screenVideoRef}
                          autoPlay
                          playsInline
                          className="w-full h-full object-contain bg-black"
                        />
                        <motion.div
                          initial={{ scale: 0.8, opacity: 0 }}
                          animate={{ scale: 1, opacity: 1 }}
                          className="absolute bottom-6 right-6 w-56 h-40 bg-black rounded-2xl overflow-hidden border-4 border-white/20 shadow-2xl"
                        >
                          <video
                            ref={pipCameraRef}
                            autoPlay
                            playsInline
                            muted
                            className="w-full h-full object-cover"
                          />
                        </motion.div>
                        <div className="absolute top-6 left-6">
                          <motion.button
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            onClick={stopScreenShare}
                            className="px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl font-medium"
                          >
                            Stop Screen Share
                          </motion.button>
                        </div>

                        {/* Live Transcription Overlay - Screen Share View */}
                        {isTranscriptionEnabled && currentTranscription && (
                          <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: 20 }}
                            className="absolute bottom-6 left-6 right-6 bg-black/80 backdrop-blur-sm text-white p-4 rounded-lg border border-white/20"
                          >
                            <div className="flex items-center space-x-2 mb-2">
                              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                              <span className="text-xs font-medium text-gray-300">
                                LIVE TRANSCRIPTION
                              </span>
                            </div>
                            <p className="text-sm leading-relaxed">{currentTranscription}</p>
                          </motion.div>
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </motion.div>

            {/* Sidebar */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="w-full xl:w-96 flex-shrink-0 space-y-6"
            >
              {/* Camera Preview */}
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                  {isScreenSharing ? 'Camera (PiP Mode)' : 'Camera Preview'}
                </h3>
                <motion.div
                  className="bg-black rounded-xl overflow-hidden"
                  style={{ aspectRatio: '4/3' }}
                >
                  <video
                    ref={videoRef}
                    autoPlay
                    playsInline
                    muted
                    className="w-full h-full object-cover"
                  />
                </motion.div>
              </div>

              {/* Tabbed Info Panel */}
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20">
                {/* Tab Buttons */}
                <div className="flex border-b border-gray-200">
                  <button
                    onClick={() => setActiveSidebarTab('status')}
                    className={`flex-1 p-4 flex items-center justify-center gap-2 text-sm font-medium transition-colors ${
                      activeSidebarTab === 'status'
                        ? 'text-blue-600 bg-blue-50'
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                      />
                    </svg>
                    <span>Status</span>
                  </button>
                  <button
                    onClick={() => setActiveSidebarTab('viewers')}
                    className={`flex-1 p-4 flex items-center justify-center gap-2 text-sm font-medium transition-colors relative ${
                      activeSidebarTab === 'viewers'
                        ? 'text-blue-600 bg-blue-50'
                        : 'text-gray-600 hover:bg-gray-50'
                    }`}
                  >
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                      />
                    </svg>
                    <span>Viewers</span>
                    <span className="absolute top-3 right-3 bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full text-xs font-bold">
                      {joinedViewers.length}
                    </span>
                  </button>
                </div>

                {/* Tab Content */}
                <div className="p-6">
                  <AnimatePresence mode="wait">
                    {activeSidebarTab === 'status' && (
                      <motion.div
                        key="status"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                      >
                        <div className="space-y-3">
                          <div className="flex items-center justify-between p-3 bg-blue-50 rounded-xl">
                            <span className="text-sm font-medium text-gray-700">LiveKit</span>
                            <span
                              className={`text-xs px-2 py-1 rounded-full ${
                                livekitConnected
                                  ? 'bg-emerald-100 text-emerald-700'
                                  : 'bg-gray-100 text-gray-600'
                              }`}
                            >
                              {livekitConnected ? 'Connected' : 'Disconnected'}
                            </span>
                          </div>
                          <div className="flex items-center justify-between p-3 bg-purple-50 rounded-xl">
                            <span className="text-sm font-medium text-gray-700">Camera</span>
                            <span
                              className={`text-xs px-2 py-1 rounded-full ${
                                cameraPermissionGranted
                                  ? 'bg-emerald-100 text-emerald-700'
                                  : 'bg-gray-100 text-gray-600'
                              }`}
                            >
                              {cameraPermissionGranted ? 'Ready' : 'Not Ready'}
                            </span>
                          </div>
                          <div className="flex items-center justify-between p-3 bg-orange-50 rounded-xl">
                            <span className="text-sm font-medium text-gray-700">Screen Share</span>
                            <span
                              className={`text-xs px-2 py-1 rounded-full ${
                                isScreenSharing
                                  ? 'bg-emerald-100 text-emerald-700'
                                  : 'bg-gray-100 text-gray-600'
                              }`}
                            >
                              {isScreenSharing ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                          <div className="flex items-center justify-between p-3 bg-green-50 rounded-xl">
                            <span className="text-sm font-medium text-gray-700">Chat</span>
                            <span
                              className={`text-xs px-2 py-1 rounded-full ${
                                socketConnected
                                  ? 'bg-emerald-100 text-emerald-700'
                                  : 'bg-gray-100 text-gray-600'
                              }`}
                            >
                              {socketConnected ? 'Connected' : 'Disconnected'}
                            </span>
                          </div>
                        </div>
                      </motion.div>
                    )}
                    {activeSidebarTab === 'viewers' && (
                      <motion.div
                        key="viewers"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                      >
                        <div className="space-y-3 max-h-64 overflow-y-auto">
                          {joinedViewers.length === 0 ? (
                            <div className="text-center py-8 text-gray-500">No viewers yet.</div>
                          ) : (
                            joinedViewers.map((viewer) => (
                              <div
                                key={viewer.viewer_id}
                                className="flex items-center justify-between p-3 bg-gray-50 rounded-xl"
                              >
                                <div className="flex items-center space-x-3">
                                  <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                                    {viewer.viewer_name?.charAt(0)?.toUpperCase() || 'U'}
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium text-gray-800">
                                      {viewer.viewer_name}
                                    </p>
                                    <span
                                      className={`text-xs px-2 py-0.5 rounded-full ${getRoleColor(
                                        viewer.user_role
                                      )} bg-opacity-10 bg-current`}
                                    >
                                      {getRoleBadge(viewer.user_role)}
                                    </span>
                                  </div>
                                </div>
                              </div>
                            ))
                          )}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </motion.div>

            {/* Fixed Chat Button - Bottom Right Corner */}
            <motion.button
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              onClick={toggleChat}
              className="fixed bottom-6 right-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white p-4 rounded-full shadow-2xl transition-all duration-200 z-50"
              title="Toggle Chat"
            >
              <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
                />
              </svg>
              <AnimatePresence>
                {unreadMessages > 0 && !isChatOpen && (
                  <motion.span
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    exit={{ scale: 0 }}
                    className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-6 h-6 rounded-full flex items-center justify-center font-bold border-2 border-white"
                  >
                    {unreadMessages}
                  </motion.span>
                )}
              </AnimatePresence>
            </motion.button>

            {/* Chat Overlay - Fixed Bottom Right */}
            <AnimatePresence>
              {isChatOpen && (
                <motion.div
                  initial={{ opacity: 0, y: 20, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 20, scale: 0.95 }}
                  transition={{ duration: 0.3 }}
                  className="fixed bottom-24 right-6 w-96 h-[500px] bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 flex flex-col z-40"
                >
                  <div className="flex items-center justify-between p-4 border-b border-gray-200">
                    <h3 className="text-lg font-semibold text-gray-800">Live Chat</h3>
                    <motion.button
                      whileHover={{ scale: 1.1, rotate: 90 }}
                      onClick={toggleChat}
                      className="text-gray-500 hover:text-gray-800"
                    >
                      <svg
                        className="w-6 h-6"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M6 18L18 6M6 6l12 12"
                        />
                      </svg>
                    </motion.button>
                  </div>
                  <div className="flex-grow p-4 overflow-y-auto space-y-3">
                    {chatMessages.length === 0 ? (
                      <div className="flex items-center justify-center h-full text-center text-gray-500">
                        No messages yet.
                      </div>
                    ) : (
                      chatMessages.map((message, index) => (
                        <motion.div
                          key={`${message.id || 'msg'}-${index}`}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="bg-white rounded-xl p-3 shadow-sm border"
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <span
                                className={`text-sm font-semibold ${getRoleColor(message.sender_role)}`}
                              >
                                {message.sender_name}
                              </span>
                              <p className="text-sm text-gray-800">{message.message}</p>
                            </div>
                            <span className="text-xs text-gray-400 ml-2">
                              {formatMessageTime(message.timestamp)}
                            </span>
                          </div>
                        </motion.div>
                      ))
                    )}
                  </div>
                  <div className="p-4 border-t border-gray-200 flex space-x-2">
                    <input
                      type="text"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder="Type a message..."
                      className="flex-1 px-4 py-2 bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={!socketConnected}
                    />
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={sendChatMessage}
                      disabled={!newMessage.trim() || !socketConnected}
                      className="p-3 bg-blue-600 text-white rounded-xl disabled:bg-gray-400"
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                        />
                      </svg>
                    </motion.button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Quiz Upload Modal */}
            <AnimatePresence>
              {showQuizUpload && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
                  onClick={() => setShowQuizUpload(false)}
                >
                  <motion.div
                    initial={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0.9, opacity: 0 }}
                    className="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-2xl font-bold text-gray-800">Generate Quiz</h3>
                      <motion.button
                        whileHover={{ scale: 1.1, rotate: 90 }}
                        onClick={() => setShowQuizUpload(false)}
                        className="text-gray-500 hover:text-gray-800"
                      >
                        <svg
                          className="w-6 h-6"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </motion.button>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Upload PDF File
                        </label>
                        <input
                          type="file"
                          accept=".pdf"
                          onChange={handleQuizFileChange}
                          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        />
                        {quizFile && (
                          <p className="text-sm text-green-600 mt-2">✅ {quizFile.name} selected</p>
                        )}
                      </div>

                      <div className="flex space-x-3 pt-4">
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={handleUploadQuiz}
                          disabled={!quizFile || isUploadingQuiz}
                          className="flex-1 px-6 py-3 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl hover:from-purple-600 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          {isUploadingQuiz ? (
                            <span className="flex items-center justify-center space-x-2">
                              <motion.div
                                animate={{ rotate: 360 }}
                                transition={{ repeat: Infinity, duration: 1 }}
                                className="w-4 h-4 border-2 border-white border-t-transparent rounded-full"
                              />
                              <span>Uploading...</span>
                            </span>
                          ) : (
                            'Generate Quiz'
                          )}
                        </motion.button>
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => setShowQuizUpload(false)}
                          className="px-6 py-3 bg-gray-200 text-gray-800 rounded-xl hover:bg-gray-300 transition-all duration-200 font-medium"
                        >
                          Cancel
                        </motion.button>
                      </div>
                    </div>
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        ) : (
          /* Enhanced Start Streaming Screen */
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            transition={{ duration: 0.6, ease: 'easeOut' }}
            className="bg-white/90 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/30 p-16 text-center max-w-3xl mx-auto relative overflow-hidden"
          >
            {/* Background decorations */}
            <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-br from-blue-200/30 to-indigo-200/20 rounded-full blur-3xl transform translate-x-20 -translate-y-20" />
            <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-purple-200/30 to-pink-200/20 rounded-full blur-3xl transform -translate-x-16 translate-y-16" />

            <div className="relative z-10">
              <motion.div
                whileHover={{ scale: 1.1, rotate: 5 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                className="w-32 h-32 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl"
              >
                <motion.svg
                  className="w-16 h-16 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  whileHover={{ scale: 1.1 }}
                  transition={{ duration: 0.2 }}
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </motion.svg>
              </motion.div>

              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.5 }}
                className="text-4xl sm:text-5xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent mb-6"
              >
                Ready to Go Live? ✨
              </motion.h2>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.5 }}
                className="text-gray-600 mb-10 text-xl font-medium"
              >
                {streamStatus}
              </motion.p>

              {error && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="mb-8 p-6 bg-red-50/80 backdrop-blur-sm border border-red-200/50 rounded-2xl text-red-700 shadow-lg"
                >
                  <div className="flex items-center justify-center space-x-2">
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <span className="font-medium">{error}</span>
                  </div>
                </motion.div>
              )}

              <motion.button
                whileHover={{ scale: 1.05, y: -3 }}
                whileTap={{ scale: 0.95 }}
                transition={{ type: 'spring', stiffness: 400, damping: 17 }}
                onClick={startStreaming}
                className="px-16 py-5 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white rounded-3xl font-bold text-xl shadow-2xl hover:shadow-3xl transition-all duration-300 relative overflow-hidden group"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <span className="relative flex items-center justify-center space-x-3">
                  <motion.svg
                    className="w-6 h-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    whileHover={{ x: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 5a9 9 0 1118 0 9 9 0 01-18 0z"
                    />
                  </motion.svg>
                  <span>Start Live Streaming</span>
                  <motion.div
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                    className="w-2 h-2 bg-white rounded-full"
                  />
                </span>
              </motion.button>

              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5, duration: 0.5 }}
                className="mt-8 text-sm text-gray-500 space-y-2"
              >
                <p>🎥 Camera and microphone will be activated</p>
                <p>📡 Live streaming will begin immediately</p>
                <p>💬 Chat and transcription features available</p>
              </motion.div>
            </div>
          </motion.div>
        )}
      </div>

      {/* Toast Notifications */}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </div>
  );
};

export default TeacherLiveStreaming;

// import { useState, useEffect, useRef } from 'react';
// import {
//   Room,
//   RoomEvent,
//   createLocalVideoTrack,
//   createLocalAudioTrack
// } from 'livekit-client';
// import { useDispatch, useSelector } from 'react-redux';
// import { motion, AnimatePresence } from 'framer-motion';
// import { toast, ToastContainer } from 'react-toastify';
// import 'react-toastify/dist/ReactToastify.css';
// import webrtcToRtmpService from '../../../../services/webrtcToRtmpService';
// import streamingServiceWorkerManager from '../../../../services/streamingServiceWorkerManager';
// import robustStreamingManager from '../../../../services/robustStreamingManager';
// import livekitBackgroundProtection from '../../../../utils/livekitBackgroundProtection';
// import bulletproofWebSocket from '../../../../utils/bulletproofWebSocket';
// import connectionQualityMonitor from '../../../../utils/connectionQualityMonitor';
// import io from 'socket.io-client';
// import {
//   useStartEnhancedStreamMutation,
//   useStopEnhancedStreamMutation,
//   useSendChatMessageMutation,
//   useLazyGetChatHistoryQuery,
//   useUploadCbtPaperMutation,
//   setStreamingData,
//   setIsStreaming,
//   setError,
//   setChatMessages,
//   setChatLoading,
//   setChatError,
//   clearStreamingData
// } from './teacherLiveStreaming.slice';

// const TeacherLiveStreaming = () => {
//   const dispatch = useDispatch();
//   const { isStreaming, streamToken, roomName, error, chatMessages, chatLoading, chatError } =
//     useSelector((state) => state.liveStreaming || {});

//   const [startStream] = useStartEnhancedStreamMutation();
//   const [stopStream] = useStopEnhancedStreamMutation();
//   const [sendChatMessageMutation] = useSendChatMessageMutation();
//   const [getChatHistory] = useLazyGetChatHistoryQuery();
//   const [uploadCbtPaper] = useUploadCbtPaperMutation();

//   // LiveKit states
//   const [livekitRoom, setLivekitRoom] = useState(null);
//   const [livekitConnected, setLivekitConnected] = useState(false);
//   const [livekitToken, setLivekitToken] = useState(null);
//   const [livekitUrl, setLivekitUrl] = useState(null);
//   const [sessionId, setSessionId] = useState(null);

//   // Media states
//   const [localVideoTrack, setLocalVideoTrack] = useState(null);
//   const [localAudioTrack, setLocalAudioTrack] = useState(null);
//   const [localScreenTrack, setLocalScreenTrack] = useState(null);
//   const [localScreenAudioTrack, setLocalScreenAudioTrack] = useState(null);
//   const [isScreenSharing, setIsScreenSharing] = useState(false);
//   const [previewVideoTrack, setPreviewVideoTrack] = useState(null);
//   const [cameraPermissionGranted, setCameraPermissionGranted] = useState(false);
//   const [microphonePermissionGranted, setMicrophonePermissionGranted] = useState(false);
//   const [screenStream, setScreenStream] = useState(null);

//   // UI states
//   const [streamStatus, setStreamStatus] = useState('Ready to start streaming');
//   const [participants, setParticipants] = useState([]);
//   const [quality, setQuality] = useState('medium');
//   const [activeSidebarTab, setActiveSidebarTab] = useState('status');
//   const [isLoading, setIsLoading] = useState(false);

//   // Chat states
//   const [newMessage, setNewMessage] = useState('');
//   const [isChatOpen, setIsChatOpen] = useState(false);
//   const [unreadMessages, setUnreadMessages] = useState(0);
//   const [joinedViewers, setJoinedViewers] = useState([]);

//   // Socket.IO states
//   const [socketConnected, setSocketConnected] = useState(false);

//   // Quiz states
//   const [showQuizUpload, setShowQuizUpload] = useState(false);
//   const [quizFile, setQuizFile] = useState(null);
//   const [isUploadingQuiz, setIsUploadingQuiz] = useState(false);

//   // Transcription states
//   const [isTranscriptionEnabled, setIsTranscriptionEnabled] = useState(false);
//   const [speechRecognition, setSpeechRecognition] = useState(null);
//   const [currentTranscription, setCurrentTranscription] = useState('');

//   // Connection monitoring states
//   const [iceConnectionState, setIceConnectionState] = useState('new');
//   const [isReconnecting, setIsReconnecting] = useState(false);
//   const [reconnectAttempts, setReconnectAttempts] = useState(0);
//   const [lastReconnectTime, setLastReconnectTime] = useState(null);
//   const [connectionQuality, setConnectionQuality] = useState('good');
//   const [wakeLock, setWakeLock] = useState(null);
//   const [isBackgroundStreaming, setIsBackgroundStreaming] = useState(false);
//   const [keepAliveInterval, setKeepAliveInterval] = useState(null);
//   const [tokenRefreshInterval, setTokenRefreshInterval] = useState(null);
//   const [sourceLanguage, setSourceLanguage] = useState('en');
//   const [transcriptionStatus, setTranscriptionStatus] = useState('');
//   const [microphoneActive, setMicrophoneActive] = useState(false);

//   // Service Worker states
//   const [serviceWorkerReady, setServiceWorkerReady] = useState(false);
//   const [backgroundStreamingActive, setBackgroundStreamingActive] = useState(false);
//   const [serviceWorkerState, setServiceWorkerState] = useState(null);
//   const [lastServiceWorkerHeartbeat, setLastServiceWorkerHeartbeat] = useState(null);



//   // Toast notification throttling
//   const [lastWakeToastTime, setLastWakeToastTime] = useState(0);
//   const [lastDisconnectToastTime, setLastDisconnectToastTime] = useState(0);

//   // RTMP streaming states
//   const [streamingMethod, setStreamingMethod] = useState('webrtc'); // 'webrtc' or 'rtmp'
//   const [rtmpConfig, setRtmpConfig] = useState(null);
//   const [rtmpSocket, setRtmpSocket] = useState(null);
//   const [rtmpConnected, setRtmpConnected] = useState(false);
//   const [rtmpStreaming, setRtmpStreaming] = useState(false);
//   const [rtmpStatus, setRtmpStatus] = useState('Ready');
//   const [showStreamingMethodSelector, setShowStreamingMethodSelector] = useState(false);
//   const TOAST_THROTTLE_INTERVAL = 30000; // 30 seconds

//   // Refs
//   const videoRef = useRef(null);
//   const screenVideoRef = useRef(null);
//   const pipCameraRef = useRef(null);
//   const livekitRoomRef = useRef(null);
//   const sharedMicrophoneStreamRef = useRef(null);
//   const chatContainerRef = useRef(null);
//   const reconnectAttemptsRef = useRef(0);
//   const maxReconnectAttempts = 5;
//   const reconnectIntervalRef = useRef(null);
//   const pageVisibilityRef = useRef(true);
//   const streamingActiveRef = useRef(false);

//   // Available languages for transcription
//   const availableSourceLanguages = [
//     { code: 'en', name: 'English', flag: '🇺🇸' },
//     { code: 'ta', name: 'Tamil', flag: '🇮🇳' },
//     { code: 'hi', name: 'Hindi', flag: '🇮🇳' },
//     { code: 'te', name: 'Telugu', flag: '🇮🇳' },
//     { code: 'kn', name: 'Kannada', flag: '🇮🇳' }
//   ];

//   // Request Wake Lock to prevent system sleep with throttled notifications
//   const requestWakeLock = async () => {
//     try {
//       if ('wakeLock' in navigator) {
//         const wakeLockSentinel = await navigator.wakeLock.request('screen');
//         setWakeLock(wakeLockSentinel);
//         console.log('✅ Wake Lock acquired');
//         setStreamStatus('Wake Lock enabled to prevent system sleep');

//         // Throttled toast notification
//         const now = Date.now();
//         if (now - lastWakeToastTime > TOAST_THROTTLE_INTERVAL) {
//           toast.info('Wake Lock enabled to keep system active');
//           setLastWakeToastTime(now);
//         }

//         // Handle visibility change to reacquire wake lock
//         const handleVisibilityChange = () => {
//           pageVisibilityRef.current = document.visibilityState === 'visible';
//           if (document.visibilityState === 'visible' && !wakeLock && streamingActiveRef.current) {
//             requestWakeLock();
//           }
//         };

//         document.addEventListener('visibilitychange', handleVisibilityChange);
//         return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
//       } else {
//         console.warn('⚠️ Wake Lock API not supported in this browser');
//         setStreamStatus('Wake Lock not supported');
//         const now = Date.now();
//         if (now - lastWakeToastTime > TOAST_THROTTLE_INTERVAL) {
//           toast.warn('Wake Lock API not supported, system may sleep');
//           setLastWakeToastTime(now);
//         }
//       }
//     } catch (err) {
//       console.error('❌ Failed to acquire Wake Lock:', err);
//       setStreamStatus('Failed to enable Wake Lock');
//       const now = Date.now();
//       if (now - lastWakeToastTime > TOAST_THROTTLE_INTERVAL) {
//         toast.error('Failed to prevent system sleep');
//         setLastWakeToastTime(now);
//       }
//     }
//   };

//   // Release Wake Lock
//   const releaseWakeLock = async () => {
//     if (wakeLock) {
//       try {
//         await wakeLock.release();
//         setWakeLock(null);
//         console.log('✅ Wake Lock released');
//       } catch (err) {
//         console.error('❌ Failed to release Wake Lock:', err);
//       }
//     }
//   };

//   // Token refresh mechanism for long streaming sessions
//   const setupTokenRefresh = () => {
//     // Refresh token every 5 seconds (for testing, will change to 45 minutes later)
//     const refreshInterval = setInterval(async () => {
//       if (streamingActiveRef.current && livekitRoom && livekitConnected) {
//         try {
//           console.log('🔄 Refreshing LiveKit token...');

//           // Call backend to get new token
//           const response = await fetch('https://sasthra.in/api/enhanced-stream/refresh-token', {
//             method: 'POST',
//             headers: {
//               'Content-Type': 'application/json',
//             },
//             body: JSON.stringify({
//               session_id: sessionId,
//               room_name: livekitRoom.name
//             })
//           });

//           if (response.ok) {
//             const data = await response.json();
//             const newToken = data.livekit_token;

//             // Update room with new token
//             await livekitRoom.engine.updateToken(newToken);
//             setLivekitToken(newToken);

//             console.log('✅ LiveKit token refreshed successfully');
//           } else {
//             console.warn('⚠️ Failed to refresh token:', response.status);
//           }
//         } catch (error) {
//           console.error('❌ Token refresh failed:', error);
//         }
//       }
//     }, 5 * 1000); // 5 seconds

//     setTokenRefreshInterval(refreshInterval);
//     return refreshInterval;
//   };

//   // Clear token refresh interval
//   const clearTokenRefresh = () => {
//     if (tokenRefreshInterval) {
//       clearInterval(tokenRefreshInterval);
//       setTokenRefreshInterval(null);
//     }
//   };

//   // ROBUST LiveKit Connection Management
//   useEffect(() => {
//     const initializeLiveKitProtection = () => {
//       console.log('🔧 Initializing ROBUST LiveKit protection...');

//       // 1. Page Visibility API - Primary protection
//       const handleVisibilityChange = () => {
//         const isVisible = !document.hidden;
//         console.log('👁️ Page visibility changed:', isVisible ? 'visible' : 'hidden');

//         if (!isVisible && livekitRoom && livekitRoom.state === 'connected') {
//           console.log('🛡️ Tab hidden - activating LiveKit protection');

//           // Force keep connection alive
//           if (livekitRoom.engine && livekitRoom.engine.client) {
//             // Prevent WebSocket from being throttled
//             const ws = livekitRoom.engine.client.ws;
//             if (ws && ws.readyState === WebSocket.OPEN) {
//               // Send periodic pings to keep connection alive
//               const keepAliveInterval = setInterval(() => {
//                 if (ws.readyState === WebSocket.OPEN && !document.hidden === false) {
//                   try {
//                     ws.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
//                   } catch (error) {
//                     console.warn('⚠️ Failed to send keep-alive ping:', error);
//                   }
//                 } else {
//                   clearInterval(keepAliveInterval);
//                 }
//               }, 5000); // Every 5 seconds

//               // Store interval for cleanup
//               window.livekitKeepAliveInterval = keepAliveInterval;
//             }
//           }

//           // Force tracks to stay active
//           if (localVideoTrack && localVideoTrack.mediaStreamTrack) {
//             try {
//               // Ensure track stays enabled
//               localVideoTrack.mediaStreamTrack.enabled = true;
//             } catch (error) {
//               console.warn('⚠️ Failed to keep video track active:', error);
//             }
//           }

//           if (localAudioTrack && localAudioTrack.mediaStreamTrack) {
//             try {
//               // Ensure track stays enabled
//               localAudioTrack.mediaStreamTrack.enabled = true;
//             } catch (error) {
//               console.warn('⚠️ Failed to keep audio track active:', error);
//             }
//           }
//         } else if (isVisible && livekitRoom) {
//           console.log('✅ Tab visible - verifying LiveKit connection');

//           // Clean up keep-alive interval
//           if (window.livekitKeepAliveInterval) {
//             clearInterval(window.livekitKeepAliveInterval);
//             window.livekitKeepAliveInterval = null;
//           }

//           // Verify connection state
//           if (livekitRoom.state === 'disconnected') {
//             console.log('🔄 Connection lost, attempting reconnection...');
//             connectToLiveKitRoom();
//           }
//         }
//       };

//       // 2. Multiple event listeners for comprehensive coverage
//       document.addEventListener('visibilitychange', handleVisibilityChange);

//       // 3. Page lifecycle events
//       window.addEventListener('pagehide', () => {
//         console.log('📴 Page hide - maintaining LiveKit connection');
//         if (livekitRoom && livekitRoom.state === 'connected') {
//           // Mark connection as background
//           livekitRoom.engine?.client?.sendRequest({
//             case: 'updateSubscription',
//             value: { trackSids: [], subscribe: true, participantTracks: [] }
//           }).catch(console.warn);
//         }
//       });

//       window.addEventListener('pageshow', () => {
//         console.log('🔍 Page show - verifying LiveKit connection');
//         if (livekitRoom && livekitRoom.state === 'disconnected') {
//           connectToLiveKitRoom();
//         }
//       });

//       // 4. Focus/blur events
//       window.addEventListener('focus', () => {
//         console.log('🔍 Window focus - checking LiveKit state');
//         if (livekitRoom && livekitRoom.state === 'disconnected') {
//           connectToLiveKitRoom();
//         }
//       });

//       window.addEventListener('blur', () => {
//         console.log('🌫️ Window blur - maintaining LiveKit connection');
//         // Keep connection active
//       });

//       // 5. Before unload protection
//       const handleBeforeUnload = (e) => {
//         if (isStreaming && livekitRoom && livekitRoom.state === 'connected') {
//           e.preventDefault();
//           e.returnValue = 'Streaming is active. Are you sure you want to leave?';
//           return e.returnValue;
//         }
//       };

//       window.addEventListener('beforeunload', handleBeforeUnload);

//       console.log('✅ LiveKit protection initialized');

//       // Cleanup function
//       return () => {
//         document.removeEventListener('visibilitychange', handleVisibilityChange);
//         window.removeEventListener('pagehide', handleVisibilityChange);
//         window.removeEventListener('pageshow', handleVisibilityChange);
//         window.removeEventListener('focus', handleVisibilityChange);
//         window.removeEventListener('blur', handleVisibilityChange);
//         window.removeEventListener('beforeunload', handleBeforeUnload);

//         if (window.livekitKeepAliveInterval) {
//           clearInterval(window.livekitKeepAliveInterval);
//           window.livekitKeepAliveInterval = null;
//         }
//       };
//     };

//     const cleanup = initializeLiveKitProtection();

//     return cleanup;
//   }, [livekitRoom, localVideoTrack, localAudioTrack, isStreaming]);

//   // Listen for reconnection events from background protection utility
//   useEffect(() => {
//     const handleReconnectionRequired = (event) => {
//       console.log('🔄 Background protection requesting reconnection:', event.detail);

//       if (isStreaming && livekitRoom && livekitRoom.state === 'disconnected') {
//         console.log('🔄 Triggering reconnection from background protection...');
//         connectToLiveKitRoom();
//       }
//     };

//     window.addEventListener('livekit-reconnection-required', handleReconnectionRequired);

//     // Listen for connection quality reconnection events
//     const handleQualityReconnection = (event) => {
//       console.log('📊 Connection quality requesting reconnection:', event.detail);

//       if (isStreaming && livekitRoom) {
//         console.log('🔄 Triggering reconnection due to poor connection quality...');
//         connectToLiveKitRoom();
//       }
//     };

//     window.addEventListener('connection-quality-reconnection-required', handleQualityReconnection);

//     return () => {
//       window.removeEventListener('livekit-reconnection-required', handleReconnectionRequired);
//       window.removeEventListener('connection-quality-reconnection-required', handleQualityReconnection);
//     };
//   }, [isStreaming, livekitRoom]);

//   // BULLETPROOF initialization on mount
//   useEffect(() => {
//     const initializeBulletproofSystem = async () => {
//       try {
//         console.log('🛡️ Initializing BULLETPROOF streaming system...');

//         // 1. Ensure bulletproof WebSocket is active
//         if (!bulletproofWebSocket.isActive) {
//           bulletproofWebSocket.initialize();
//         }

//         // 2. Check permissions
//         const cameraPermission = await navigator.permissions.query({ name: 'camera' });
//         const micPermission = await navigator.permissions.query({ name: 'microphone' });
//         setCameraPermissionGranted(cameraPermission.state === 'granted');
//         setMicrophonePermissionGranted(micPermission.state === 'granted');

//         if (!window.isSecureContext) {
//           console.error('❌ App not running in secure context. Camera access requires HTTPS.');
//           setStreamStatus('Error: Camera access requires HTTPS.');
//           dispatch(setError('Camera access requires a secure context (HTTPS).'));
//         }

//         // 3. Set up global error handlers for WebRTC
//         window.addEventListener('unhandledrejection', (event) => {
//           if (event.reason && event.reason.toString().includes('livekit')) {
//             console.warn('🛡️ BULLETPROOF: Caught LiveKit error:', event.reason);
//             event.preventDefault(); // Prevent error from breaking the app
//           }
//         });

//         console.log('✅ BULLETPROOF streaming system initialized');

//       } catch (err) {
//         console.error('❌ Error initializing BULLETPROOF system:', err);
//       }
//     };

//     initializeBulletproofSystem();

//     return () => {
//       // Cleanup on unmount
//       if (bulletproofWebSocket.isActive) {
//         bulletproofWebSocket.restore();
//       }
//     };
//   }, [dispatch]);

//   // Initialize session ID and store in sessionStorage
//   useEffect(() => {
//     const userId = sessionStorage.getItem('userId');
//     const newSessionId = `teacher_${userId}_${Date.now()}`;
//     setSessionId(newSessionId);
//     sessionStorage.setItem('streamSessionId', newSessionId);
//   }, []);

//   // Camera preview initialization
//   useEffect(() => {
//     if (isStreaming) {
//       initializeCameraPreview();
//     }
//     return () => {
//       if (!isStreaming && previewVideoTrack) {
//         previewVideoTrack.stop();
//         setPreviewVideoTrack(null);
//       }
//     };
//   }, [isStreaming]);

//   // Attach video track
//   useEffect(() => {
//     if (previewVideoTrack && videoRef.current && !isScreenSharing) {
//       previewVideoTrack.attach(videoRef.current);
//       console.log('✅ Preview video track attached');
//     }
//     return () => {
//       if (previewVideoTrack && videoRef.current) {
//         previewVideoTrack.detach(videoRef.current);
//       }
//     };
//   }, [previewVideoTrack, isScreenSharing]);

//   // Connect to LiveKit
//   useEffect(() => {
//     if (livekitToken && livekitUrl && !livekitRoom) {
//       connectToLiveKitRoom();
//     }
//     return () => {
//       if (livekitRoom) {
//         cleanupLiveKitResources();
//       }
//     };
//   }, [livekitToken, livekitUrl, livekitRoom]);

//   // Publish camera track
//   useEffect(() => {
//     const publishCameraTrack = async () => {
//       if (localVideoTrack && livekitRoom && livekitConnected) {
//         try {
//           const publishedTracks = Array.from(livekitRoom.localParticipant.videoTracks.values());
//           if (!publishedTracks.some(pub => pub.source === 'camera' || pub.trackName === 'teacher_camera')) {
//             await livekitRoom.localParticipant.publishTrack(localVideoTrack, {
//               source: 'camera',
//               name: 'teacher_camera'
//             });
//             console.log('✅ Camera track published to LiveKit');
//           }
//         } catch (err) {
//           console.error('❌ Failed to publish camera track:', err);
//           toast.error('Failed to publish camera track');
//         }
//       }
//     };
//     publishCameraTrack();
//   }, [localVideoTrack, livekitRoom, livekitConnected]);



//   // Screen stream handling
//   useEffect(() => {
//     if (screenStream && screenVideoRef.current && isScreenSharing) {
//       screenVideoRef.current.srcObject = screenStream;
//       screenVideoRef.current.play().catch(err => {
//         console.warn('⚠️ Screen video autoplay failed:', err);
//       });
//     }
//     return () => {
//       if (screenStream && screenVideoRef.current) {
//         screenVideoRef.current.srcObject = null;
//       }
//     };
//   }, [screenStream, isScreenSharing]);

//   // PiP camera handling
//   useEffect(() => {
//     if (isScreenSharing && previewVideoTrack && pipCameraRef.current) {
//       try {
//         previewVideoTrack.attach(pipCameraRef.current);
//         pipCameraRef.current.play().catch(err => {
//           console.warn('⚠️ PiP camera autoplay failed:', err);
//         });
//       } catch (err) {
//         console.error('❌ Failed to attach camera to PiP:', err);
//       }
//     }
//   }, [isScreenSharing, previewVideoTrack]);

//   // Enhanced keep-alive for extended streaming sessions
//   useEffect(() => {
//     let keepAliveInterval;
//     let connectionCheckInterval;

//     if (streamingActiveRef.current && livekitConnected) {
//       // Track keep-alive every 30 seconds
//       keepAliveInterval = setInterval(() => {
//         try {
//           if (localVideoTrack && localVideoTrack.mediaStreamTrack.readyState === 'live') {
//             console.log('🔄 Keeping video track alive');
//             // Don't restart if track is already live and working
//           } else if (localVideoTrack) {
//             console.log('🔄 Restarting video track');
//             localVideoTrack.restart();
//           }

//           if (localAudioTrack && localAudioTrack.mediaStreamTrack.readyState === 'live') {
//             console.log('🔄 Keeping audio track alive');
//           } else if (localAudioTrack) {
//             console.log('🔄 Restarting audio track');
//             localAudioTrack.restart();
//           }

//           if (localScreenTrack && localScreenTrack.mediaStreamTrack.readyState === 'live') {
//             console.log('🔄 Keeping screen track alive');
//           } else if (localScreenTrack) {
//             console.log('🔄 Restarting screen track');
//             localScreenTrack.restart();
//           }
//         } catch (err) {
//           console.warn('⚠️ Error in keep-alive:', err);
//         }
//       }, 30000); // Every 30 seconds

//       // Connection health check every 60 seconds
//       connectionCheckInterval = setInterval(() => {
//         if (livekitRoom && streamingActiveRef.current) {
//           const connectionState = livekitRoom.state;
//           console.log('🔍 Connection health check:', connectionState);

//           if (connectionState === 'disconnected' || connectionState === 'reconnecting') {
//             console.log('🔄 Connection unhealthy, attempting reconnect...');
//             connectToLiveKitRoom();
//           }

//           // Reacquire wake lock if lost
//           if (!wakeLock && 'wakeLock' in navigator) {
//             requestWakeLock();
//           }
//         }
//       }, 60000); // Every 60 seconds
//     }

//     return () => {
//       clearInterval(keepAliveInterval);
//       clearInterval(connectionCheckInterval);
//     };
//   }, [streamingActiveRef.current, livekitConnected, localVideoTrack, localAudioTrack, localScreenTrack, livekitRoom, wakeLock]);

//   // Token refresh mechanism (assuming backend supports it)
//   useEffect(() => {
//     let tokenRefreshInterval;
//     if (isStreaming && livekitToken) {
//       tokenRefreshInterval = setInterval(async () => {
//         try {
//           const userId = sessionStorage.getItem('userId');
//           const response = await startStream({
//             userId,
//             sessionId,
//             quality,
//             screenShareEnabled: isScreenSharing
//           }).unwrap();
//           setLivekitToken(response.livekit_token);
//           setLivekitUrl(response.livekit_url);
//           console.log('✅ LiveKit token refreshed');
//         } catch (err) {
//           console.error('❌ Failed to refresh LiveKit token:', err);
//           toast.error('Failed to refresh streaming token');
//         }
//       }, 30 * 60 * 1000); // Refresh every 30 minutes
//     }
//     return () => clearInterval(tokenRefreshInterval);
//   }, [isStreaming, livekitToken, sessionId, quality, isScreenSharing, startStream]);

//   // Chat polling (unchanged)
//   useEffect(() => {
//     if (isStreaming && sessionId) {
//       console.log('💬 TEACHER: Starting HTTP-based chat for session:', sessionId);
//       setSocketConnected(true);
//       setStreamStatus('Chat system ready');
//       loadChatHistory();
//       const pollInterval = setInterval(() => {
//         loadChatHistory();
//       }, 2000);
//       return () => {
//         clearInterval(pollInterval);
//         setSocketConnected(false);
//       };
//     }
//   }, [isStreaming, sessionId]);

//   // Auto-scroll chat
//   useEffect(() => {
//     if (isChatOpen && chatContainerRef.current) {
//       chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
//     }
//   }, [chatMessages, isChatOpen]);

//   // Enhanced tab visibility handling for background streaming
//   useEffect(() => {
//     const handleVisibilityChange = () => {
//       pageVisibilityRef.current = document.visibilityState === 'visible';

//       if (document.visibilityState === 'hidden' && streamingActiveRef.current) {
//         console.log('🔄 Tab is hidden, maintaining stream in background');
//         setIsBackgroundStreaming(true);

//         // Ensure LiveKit connection is maintained
//         if (livekitRoom && !livekitConnected && !isReconnecting) {
//           console.log('🔄 Reconnecting LiveKit due to tab visibility change');
//           connectToLiveKitRoom();
//         }

//         // Maintain wake lock if possible
//         if (!wakeLock && 'wakeLock' in navigator) {
//           requestWakeLock();
//         }

//         // Enhanced track maintenance for background streaming
//         if (livekitRoom && livekitConnected) {
//           // Check if tracks are still published and active
//           const publishedVideoTracks = Array.from(livekitRoom.localParticipant.videoTracks.values());
//           const publishedAudioTracks = Array.from(livekitRoom.localParticipant.audioTracks.values());

//           // Verify camera track
//           if (localVideoTrack && !publishedVideoTracks.some(pub => pub.track === localVideoTrack)) {
//             console.log('🔄 Re-publishing camera track for background streaming');
//             livekitRoom.localParticipant.publishTrack(localVideoTrack, {
//               source: 'camera',
//               name: 'teacher_camera'
//             }).catch(err => console.warn('⚠️ Failed to re-publish camera track:', err));
//           }

//           // Verify microphone track
//           if (localAudioTrack && !publishedAudioTracks.some(pub => pub.track === localAudioTrack)) {
//             console.log('🔄 Re-publishing microphone track for background streaming');
//             livekitRoom.localParticipant.publishTrack(localAudioTrack, {
//               source: 'microphone',
//               name: 'teacher_microphone'
//             }).catch(err => console.warn('⚠️ Failed to re-publish microphone track:', err));
//           }

//           // Verify screen sharing tracks if active
//           if (isScreenSharing) {
//             const screenTracks = Array.from(livekitRoom.localParticipant.videoTracks.values())
//               .filter(pub => pub.source === 'screen_share');

//             if (screenTracks.length === 0) {
//               console.log('🔄 Screen sharing lost during background, attempting to restart');
//               // Re-enable screen sharing
//               livekitRoom.localParticipant.setScreenShareEnabled(true)
//                 .catch(err => console.warn('⚠️ Failed to restart screen sharing:', err));
//             }
//           }
//         }

//       } else if (document.visibilityState === 'visible' && streamingActiveRef.current) {
//         console.log('✅ Tab is visible, verifying stream integrity');
//         setIsBackgroundStreaming(false);

//         // Comprehensive connection verification
//         if (livekitRoom) {
//           const connectionState = livekitRoom.state;
//           console.log('🔍 LiveKit connection state on tab focus:', connectionState);

//           if (connectionState === 'disconnected' && !isReconnecting) {
//             console.log('🔄 Reconnecting LiveKit on tab focus');
//             connectToLiveKitRoom();
//           } else if (connectionState === 'connected') {
//             // Verify all expected tracks are still published
//             const publishedVideoTracks = Array.from(livekitRoom.localParticipant.videoTracks.values());
//             const publishedAudioTracks = Array.from(livekitRoom.localParticipant.audioTracks.values());

//             console.log('📊 Published tracks on tab focus:', {
//               video: publishedVideoTracks.length,
//               audio: publishedAudioTracks.length,
//               screenSharing: isScreenSharing
//             });

//             // Re-acquire wake lock if lost
//             if (!wakeLock && 'wakeLock' in navigator) {
//               requestWakeLock();
//             }
//           }
//         }
//       }
//     };

//     const handleBeforeUnload = (e) => {
//       if (streamingActiveRef.current) {
//         e.preventDefault();
//         e.returnValue = 'You are currently streaming. Are you sure you want to leave?';
//         return e.returnValue;
//       }
//     };

//     document.addEventListener('visibilitychange', handleVisibilityChange);
//     window.addEventListener('beforeunload', handleBeforeUnload);

//     return () => {
//       document.removeEventListener('visibilitychange', handleVisibilityChange);
//       window.removeEventListener('beforeunload', handleBeforeUnload);
//     };
//   }, [streamingActiveRef.current, livekitRoom, livekitConnected, localVideoTrack, localAudioTrack, localScreenTrack, wakeLock]);

//   // Sync streaming active reference with isStreaming state
//   useEffect(() => {
//     streamingActiveRef.current = isStreaming;
//   }, [isStreaming]);

//   // Cleanup on unmount
//   useEffect(() => {
//     return () => {
//       streamingActiveRef.current = false;
//       cleanupAllResources();
//       releaseWakeLock();
//       sessionStorage.removeItem('streamSessionId');
//     };
//   }, []);

//   // Permission check before streaming
//   const checkMediaPermissions = async () => {
//     try {
//       const stream = await navigator.mediaDevices.getUserMedia({
//         video: true,
//         audio: true
//       });
//       stream.getTracks().forEach(track => track.stop());
//       setCameraPermissionGranted(true);
//       setMicrophonePermissionGranted(true);
//       return true;
//     } catch (err) {
//       console.error('❌ Media permission check failed:', err);
//       toast.error('Please grant camera and microphone permissions');
//       setStreamStatus('Media permissions required');
//       dispatch(setError('Media permissions required'));
//       return false;
//     }
//   };

//   const initializeSharedMicrophone = async () => {
//     try {
//       const microphoneStream = await navigator.mediaDevices.getUserMedia({
//         audio: {
//           echoCancellation: true,
//           noiseSuppression: true,
//           autoGainControl: true,
//           sampleRate: 44100
//         }
//       });
//       sharedMicrophoneStreamRef.current = microphoneStream;
//       setMicrophonePermissionGranted(true);
//       return microphoneStream;
//     } catch (err) {
//       console.error('❌ Failed to initialize shared microphone:', err);
//       toast.error('Microphone access denied');
//       throw err;
//     }
//   };

//   const initializeCameraPreview = async () => {
//     setIsLoading(true);
//     try {
//       const videoTrack = await createLocalVideoTrack({
//         resolution: { width: 640, height: 480 },
//         frameRate: 15
//       });
//       setPreviewVideoTrack(videoTrack);
//       setCameraPermissionGranted(true);
//       setStreamStatus('Camera initialized');
//     } catch (err) {
//       console.error('❌ Error initializing camera:', err);
//       let errorMessage = 'Failed to initialize camera';
//       if (err.name === 'NotAllowedError') {
//         errorMessage = 'Camera permission denied';
//       } else if (err.name === 'NotFoundError') {
//         errorMessage = 'No camera found';
//       }
//       setStreamStatus(errorMessage);
//       dispatch(setError(errorMessage));
//       toast.error(errorMessage);
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   // ICE connection monitoring
//   const setupICEConnectionMonitoring = (room) => {
//     if (!room || !room.engine || !room.engine.pcManager) return;

//     const pcManager = room.engine.pcManager;

//     // Monitor ICE connection state changes
//     const handleICEConnectionStateChange = () => {
//       const pc = pcManager.publisher?.pc || pcManager.subscriber?.pc;
//       if (pc) {
//         const state = pc.iceConnectionState;
//         console.log('🧊 ICE Connection State:', state);
//         setIceConnectionState(state);

//         switch (state) {
//           case 'disconnected':
//           case 'failed':
//             console.log('🚨 ICE connection failed, attempting restart...');
//             handleICERestart(room);
//             break;
//           case 'connected':
//           case 'completed':
//             console.log('✅ ICE connection established');
//             setConnectionQuality('good');
//             break;
//           case 'checking':
//             setConnectionQuality('poor');
//             break;
//         }
//       }
//     };

//     // Set up ICE connection monitoring with delay to ensure PC is ready
//     setTimeout(() => {
//       if (pcManager.publisher?.pc) {
//         pcManager.publisher.pc.addEventListener('iceconnectionstatechange', handleICEConnectionStateChange);
//       }
//       if (pcManager.subscriber?.pc) {
//         pcManager.subscriber.pc.addEventListener('iceconnectionstatechange', handleICEConnectionStateChange);
//       }
//     }, 1000);
//   };

//   // Handle ICE restart
//   const handleICERestart = async (room) => {
//     try {
//       console.log('🔄 Initiating ICE restart...');

//       if (room && room.engine && room.engine.restartIce) {
//         // Trigger ICE restart through LiveKit's engine
//         await room.engine.restartIce();
//         console.log('✅ ICE restart completed');
//       }
//     } catch (error) {
//       console.error('❌ ICE restart failed:', error);
//       // Fall back to full reconnection if ICE restart fails
//       if (livekitToken && livekitUrl) {
//         setTimeout(() => {
//           reconnectAttemptsRef.current += 1;
//           connectToLiveKitRoom();
//         }, 2000);
//       }
//     }
//   };

//   // BULLETPROOF Background Protection for LiveKit - Google Meet Level
//   const startRobustBackgroundProtection = (room) => {
//     console.log('🛡️ Starting BULLETPROOF background protection for LiveKit...');

//     // 1. AGGRESSIVE WebSocket Protection with Multiple Strategies
//     const startWebSocketKeepAlive = () => {
//       if (window.livekitWSKeepAlive) {
//         clearInterval(window.livekitWSKeepAlive);
//       }

//       // Strategy 1: Direct WebSocket keep-alive
//       window.livekitWSKeepAlive = setInterval(() => {
//         if (room && room.engine && room.engine.client) {
//           const ws = room.engine.client.ws;
//           if (ws && ws.readyState === WebSocket.OPEN) {
//             try {
//               // CRITICAL: Send multiple types of keep-alive messages

//               // 1. Standard ping
//               ws.send(JSON.stringify({
//                 type: 'ping',
//                 timestamp: Date.now(),
//                 background: document.hidden
//               }));

//               // 2. Empty message to keep connection alive
//               ws.send('');

//               // 3. Heartbeat message
//               ws.send(JSON.stringify({
//                 method: 'heartbeat',
//                 id: Date.now()
//               }));

//               console.log('💓 AGGRESSIVE WebSocket keep-alive sent (Background:', document.hidden, ')');
//             } catch (error) {
//               console.warn('⚠️ Failed to send WebSocket keep-alive:', error);

//               // If WebSocket fails, force reconnection
//               if (streamingActiveRef.current) {
//                 console.log('🔄 WebSocket failed, forcing reconnection...');
//                 setTimeout(() => {
//                   connectToLiveKitRoom();
//                 }, 1000);
//               }
//             }
//           } else {
//             console.warn('⚠️ WebSocket not available or not open, state:', ws?.readyState);

//             // Force reconnection if WebSocket is not open
//             if (streamingActiveRef.current && ws?.readyState !== WebSocket.CONNECTING) {
//               console.log('🔄 WebSocket not open, forcing reconnection...');
//               setTimeout(() => {
//                 connectToLiveKitRoom();
//               }, 1000);
//             }
//           }
//         }
//       }, document.hidden ? 1000 : 15000); // EXTREMELY frequent when in background (1 second)

//       // Strategy 2: Engine-level keep-alive
//       if (window.livekitEngineKeepAlive) {
//         clearInterval(window.livekitEngineKeepAlive);
//       }

//       window.livekitEngineKeepAlive = setInterval(() => {
//         if (room && room.engine) {
//           try {
//             // Force engine to stay active
//             room.engine.client?.sendRequest({
//               case: 'ping',
//               value: { timestamp: Date.now() }
//             }).catch(() => {
//               // Ignore ping failures, just keep trying
//             });
//           } catch (error) {
//             // Ignore errors, just keep trying
//           }
//         }
//       }, document.hidden ? 2000 : 30000);
//     };

//     // 2. BULLETPROOF Track Health Monitoring with Auto-Recovery
//     const startTrackHealthMonitoring = () => {
//       if (window.livekitTrackMonitor) {
//         clearInterval(window.livekitTrackMonitor);
//       }

//       window.livekitTrackMonitor = setInterval(async () => {
//         if (!room || !streamingActiveRef.current) return;

//         console.log('🔍 BULLETPROOF track health check...');

//         try {
//           // CRITICAL: Check and maintain video track
//           if (localVideoTrack && localVideoTrack.mediaStreamTrack) {
//             const videoTrack = localVideoTrack.mediaStreamTrack;
//             const videoEnabled = videoTrack.enabled;
//             const videoReadyState = videoTrack.readyState;

//             console.log('📹 Video track status:', { enabled: videoEnabled, readyState: videoReadyState });

//             if (videoReadyState !== 'live' || !videoEnabled) {
//               console.warn('⚠️ Video track issues detected, attempting recovery...');
//               try {
//                 // Strategy 1: Re-enable track
//                 videoTrack.enabled = true;

//                 // Strategy 2: Restart track
//                 await localVideoTrack.restart();

//                 // Strategy 3: If still failing, republish
//                 if (videoTrack.readyState !== 'live') {
//                   console.log('🔄 Republishing video track...');
//                   await room.localParticipant.unpublishTrack(localVideoTrack);
//                   await room.localParticipant.publishTrack(localVideoTrack, {
//                     name: 'teacher_camera',
//                     source: 'camera',
//                     simulcast: false // Disable simulcast for stability
//                   });
//                 }
//               } catch (error) {
//                 console.error('❌ Failed to recover video track:', error);

//                 // Strategy 4: Create new track
//                 try {
//                   const newVideoTrack = await createLocalVideoTrack({
//                     deviceId: selectedCamera,
//                     resolution: { width: 1280, height: 720 },
//                     facingMode: 'user'
//                   });

//                   await room.localParticipant.unpublishTrack(localVideoTrack);
//                   setLocalVideoTrack(newVideoTrack);
//                   await room.localParticipant.publishTrack(newVideoTrack, {
//                     name: 'teacher_camera',
//                     source: 'camera',
//                     simulcast: false
//                   });

//                   console.log('✅ Video track recreated successfully');
//                 } catch (recreateError) {
//                   console.error('❌ Failed to recreate video track:', recreateError);
//                 }
//               }
//             }
//           }

//           // CRITICAL: Check and maintain audio track
//           if (localAudioTrack && localAudioTrack.mediaStreamTrack) {
//             const audioTrack = localAudioTrack.mediaStreamTrack;
//             const audioEnabled = audioTrack.enabled;
//             const audioReadyState = audioTrack.readyState;

//             console.log('🎤 Audio track status:', { enabled: audioEnabled, readyState: audioReadyState });

//             if (audioReadyState !== 'live' || !audioEnabled) {
//               console.warn('⚠️ Audio track issues detected, attempting recovery...');
//               try {
//                 // Strategy 1: Re-enable track
//                 audioTrack.enabled = true;

//                 // Strategy 2: Restart track
//                 await localAudioTrack.restart();

//                 // Strategy 3: If still failing, republish
//                 if (audioTrack.readyState !== 'live') {
//                   console.log('🔄 Republishing audio track...');
//                   await room.localParticipant.unpublishTrack(localAudioTrack);
//                   await room.localParticipant.publishTrack(localAudioTrack, {
//                     name: 'teacher_microphone',
//                     source: 'microphone'
//                   });
//                 }
//               } catch (error) {
//                 console.error('❌ Failed to recover audio track:', error);

//                 // Strategy 4: Create new track
//                 try {
//                   const newAudioTrack = await createLocalAudioTrack({
//                     deviceId: selectedMicrophone,
//                     echoCancellation: true,
//                     noiseSuppression: true,
//                     autoGainControl: true
//                   });

//                   await room.localParticipant.unpublishTrack(localAudioTrack);
//                   setLocalAudioTrack(newAudioTrack);
//                   await room.localParticipant.publishTrack(newAudioTrack, {
//                     name: 'teacher_microphone',
//                     source: 'microphone'
//                   });

//                   console.log('✅ Audio track recreated successfully');
//                 } catch (recreateError) {
//                   console.error('❌ Failed to recreate audio track:', recreateError);
//                 }
//               }
//             }
//           }
//         } catch (error) {
//           console.error('❌ Track monitoring error:', error);
//         }
//       }, 5000); // Very frequent monitoring (every 5 seconds)
//     };

//     // 3. BULLETPROOF Connection State Monitoring
//     const startConnectionStateMonitoring = () => {
//       if (window.livekitConnectionMonitor) {
//         clearInterval(window.livekitConnectionMonitor);
//       }

//       window.livekitConnectionMonitor = setInterval(() => {
//         if (!room || !streamingActiveRef.current) return;

//         const state = room.state;
//         const engineState = room.engine?.connectionState;
//         const wsState = room.engine?.client?.ws?.readyState;

//         console.log('🔍 BULLETPROOF state check:', {
//           roomState: state,
//           engineState: engineState,
//           wsState: wsState,
//           background: document.hidden
//         });

//         // CRITICAL: Multiple disconnection detection strategies
//         const isDisconnected = (
//           state === 'disconnected' ||
//           engineState === 'disconnected' ||
//           wsState === WebSocket.CLOSED ||
//           wsState === WebSocket.CLOSING
//         );

//         if (isDisconnected && streamingActiveRef.current) {
//           console.log('⚠️ BULLETPROOF: Connection lost, forcing immediate reconnection...');

//           // Clear existing intervals to prevent conflicts
//           if (window.livekitWSKeepAlive) {
//             clearInterval(window.livekitWSKeepAlive);
//             window.livekitWSKeepAlive = null;
//           }

//           if (window.livekitEngineKeepAlive) {
//             clearInterval(window.livekitEngineKeepAlive);
//             window.livekitEngineKeepAlive = null;
//           }

//           // Force immediate reconnection
//           setTimeout(() => {
//             if (streamingActiveRef.current) {
//               connectToLiveKitRoom();
//             }
//           }, 500); // Very fast reconnection
//         }
//       }, document.hidden ? 2000 : 15000); // Very frequent monitoring
//     };

//     // Start all monitoring systems
//     startWebSocketKeepAlive();
//     startTrackHealthMonitoring();
//     startConnectionStateMonitoring();

//     console.log('✅ ROBUST background protection started');
//   };

//   const connectToLiveKitRoom = async () => {
//     setIsLoading(true);
//     try {
//       // BULLETPROOF LiveKit Room Configuration - Google Meet Level
//       const room = new Room({
//         // CRITICAL: Disable adaptive stream to prevent disconnections
//         adaptiveStream: false,
//         // CRITICAL: Disable dynacast for maximum stability
//         dynacast: false,
//         // CRITICAL: Disable auto-manage to prevent interference
//         autoManageVideo: false,
//         // CRITICAL: Extended connection timeout
//         connectTimeout: 60000,
//         // CRITICAL: Disable experimental features that can cause instability
//         experimentalFeatures: {},
//         // CRITICAL: Connection quality settings for stability
//         connectionQuality: {
//           enabled: true,
//           interval: 30000
//         },
//         // CRITICAL: Reconnection settings
//         reconnectPolicy: {
//           nextRetryDelayInMs: (context) => {
//             // Immediate reconnection for background scenarios
//             if (document.hidden) {
//               return 1000; // 1 second
//             }
//             // Progressive delay for foreground
//             return Math.min(30000, 1000 * Math.pow(1.5, context.retryCount));
//           },
//           maxRetryCount: 20 // Increased retry count
//         },
//         // CRITICAL: WebRTC configuration for stability
//         webRTCConfig: {
//           iceServers: [
//             { urls: 'stun:stun.l.google.com:19302' },
//             { urls: 'stun:stun1.l.google.com:19302' },
//             { urls: 'stun:stun2.l.google.com:19302' }
//           ],
//           iceCandidatePoolSize: 10,
//           iceTransportPolicy: 'all'
//         }
//       });

//       livekitRoomRef.current = room;

//       room.on(RoomEvent.Connected, () => {
//         setLivekitConnected(true);
//         setStreamStatus('Connected to Live room');
//         toast.success('Connected to streaming server');
//         reconnectAttemptsRef.current = 0; // Reset reconnect attempts
//         setIsReconnecting(false);

//         // Set up ICE connection monitoring
//         setupICEConnectionMonitoring(room);

//         // Start ROBUST background protection
//         startRobustBackgroundProtection(room);

//         // Start ULTIMATE background protection utility
//         livekitBackgroundProtection.start(room);

//         // Start BULLETPROOF connection quality monitoring
//         connectionQualityMonitor.start(room);
//       });

//       room.on(RoomEvent.Disconnected, (reason) => {
//         setLivekitConnected(false);
//         setStreamStatus('Disconnected from Live room');

//         // Throttled disconnect notification
//         const now = Date.now();
//         if (now - lastDisconnectToastTime > TOAST_THROTTLE_INTERVAL) {
//           toast.warn('Disconnected from streaming server');
//           setLastDisconnectToastTime(now);
//         }

//         // AGGRESSIVE reconnection for background streaming
//         if (streamingActiveRef.current && reason !== 'CLIENT_INITIATED' && reconnectAttemptsRef.current < maxReconnectAttempts) {
//           console.log('🔄 ROBUST reconnection attempt...');
//           setIsReconnecting(true);

//           // Immediate reconnection for background scenarios
//           const delay = document.hidden ? 1000 : (3000 * reconnectAttemptsRef.current);

//           setTimeout(() => {
//             reconnectAttemptsRef.current += 1;
//             connectToLiveKitRoom();
//           }, delay);
//         } else if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
//           console.error('❌ Max reconnect attempts reached');
//           setStreamStatus('Failed to reconnect to streaming server');
//           toast.error('Streaming stopped due to connection issues');
//           setIsReconnecting(false);
//           stopStreaming();
//         }
//       });

//       room.on(RoomEvent.Reconnecting, () => {
//         console.log('🔄 LiveKit reconnecting...');
//         setIsReconnecting(true);
//         setStreamStatus('Reconnecting to streaming server...');
//         toast.info('Reconnecting to stream...', { toastId: 'reconnecting' });
//       });

//       room.on(RoomEvent.Reconnected, () => {
//         console.log('✅ LiveKit reconnected');
//         setIsReconnecting(false);
//         setStreamStatus('Reconnected to streaming server');
//         reconnectAttemptsRef.current = 0;
//         toast.success('Stream reconnected successfully', { toastId: 'reconnected' });

//         // Re-setup ICE monitoring after reconnection
//         setupICEConnectionMonitoring(room);
//       });

//       room.on(RoomEvent.ParticipantConnected, (participant) => {
//         setParticipants(prev => [...prev, participant]);
//         setJoinedViewers(prev => {
//           if (!prev.some(v => v.viewer_id === participant.identity)) {
//             return [
//               ...prev,
//               {
//                 viewer_id: participant.identity,
//                 viewer_name: participant.name || participant.identity,
//                 user_role: 'student',
//                 joined_at: new Date().toISOString(),
//                 source: 'livekit'
//               }
//             ];
//           }
//           return prev;
//         });
//       });

//       room.on(RoomEvent.ParticipantDisconnected, (participant) => {
//         setParticipants(prev => prev.filter(p => p.identity !== participant.identity));
//         setJoinedViewers(prev => prev.filter(v => v.viewer_id !== participant.identity));
//       });

//       await room.connect(livekitUrl, livekitToken);
//       setLivekitRoom(room);

//       // Set room in robust streaming manager for protection
//       if (robustStreamingManager.isActive) {
//         robustStreamingManager.setLiveKitRoom(room);
//       }

//       const cameraTrack = localVideoTrack || previewVideoTrack;
//       if (cameraTrack) {
//         await room.localParticipant.publishTrack(cameraTrack, {
//           source: 'camera',
//           name: 'teacher_camera'
//         });
//         setLocalVideoTrack(cameraTrack);
//       }

//       if (sharedMicrophoneStreamRef.current) {
//         const audioTrack = await createLocalAudioTrack({
//           deviceId: sharedMicrophoneStreamRef.current.getAudioTracks()[0].getSettings().deviceId
//         });
//         setLocalAudioTrack(audioTrack);
//         await room.localParticipant.publishTrack(audioTrack, {
//           name: 'teacher_microphone',
//           source: 'microphone'
//         });

//         // Update robust streaming manager with new tracks
//         if (robustStreamingManager.isActive) {
//           robustStreamingManager.setMediaTracks(cameraTrack, audioTrack);
//         }
//       }
//     } catch (err) {
//       console.error('❌ Failed to connect to Live room:', err);
//       setStreamStatus('Failed to connect to Live room');
//       dispatch(setError(err.message));
//       toast.error('Failed to connect to streaming server');
//       if (isStreaming && reconnectAttemptsRef.current < maxReconnectAttempts) {
//         console.log('🔄 Attempting to reconnect to LiveKit...');
//         setTimeout(() => {
//           reconnectAttemptsRef.current += 1;
//           connectToLiveKitRoom();
//         }, 3000 * reconnectAttemptsRef.current);
//       }
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   const loadChatHistory = async () => {
//     if (!sessionId) return;
//     try {
//       dispatch(setChatLoading(true));
//       const result = await getChatHistory(sessionId);
//       if (result.data) {
//         const newMessages = result.data;
//         const currentMessageCount = chatMessages.length;
//         if (newMessages.length > currentMessageCount && !isChatOpen) {
//           setUnreadMessages(prev => prev + (newMessages.length - currentMessageCount));
//         }
//         dispatch(setChatMessages(newMessages));
//       } else if (result.error) {
//         dispatch(setChatError(result.error.data || 'Failed to load chat history'));
//         toast.error('Failed to load chat history');
//       }
//     } catch (error) {
//       dispatch(setChatError(error.message));
//       toast.error('Error loading chat history');
//     } finally {
//       dispatch(setChatLoading(false));
//     }
//   };

//   const startStreaming = async () => {
//     if (streamingMethod === 'rtmp') {
//       await startRTMPStreaming();
//       return;
//     }

//     // Default WebRTC streaming
//     setIsLoading(true);
//     try {
//       if (!cameraPermissionGranted || !microphonePermissionGranted) {
//         const hasPermissions = await checkMediaPermissions();
//         if (!hasPermissions) return;
//       }

//       if (!previewVideoTrack) {
//         await initializeCameraPreview();
//       }

//       if (!sharedMicrophoneStreamRef.current) {
//         await initializeSharedMicrophone();
//       }

//       await requestWakeLock(); // Request Wake Lock before starting stream

//       const userId = sessionStorage.getItem('userId');
//       const response = await startStream({
//         userId,
//         sessionId,
//         quality,
//         screenShareEnabled: isScreenSharing
//       }).unwrap();

//       setSessionId(response.session_id || sessionId);
//       sessionStorage.setItem('streamSessionId', response.session_id || sessionId);
//       dispatch(setStreamingData({
//         streamToken: response.livekit_token,
//         roomName: response.roomName
//       }));
//       dispatch(setIsStreaming(true));
//       streamingActiveRef.current = true; // Set streaming active flag
//       setLivekitToken(response.livekit_token);
//       setLivekitUrl(response.livekit_url);
//       setLocalVideoTrack(previewVideoTrack);
//       setStreamStatus('Streaming started successfully');

//       // Setup token refresh for long streaming sessions
//       setupTokenRefresh();

//       // Start ROBUST background streaming with multiple protection layers
//       try {
//         // 1. Initialize robust streaming manager
//         await robustStreamingManager.initialize({
//           sessionId: response.session_id || sessionId,
//           livekitToken: response.livekit_token,
//           livekitUrl: response.livekit_url,
//           roomName: response.roomName,
//           streamUrl: response.stream_url,
//           streamingMethod: 'webrtc'
//         });

//         // 2. Set media tracks for protection
//         if (previewVideoTrack && localAudioTrack) {
//           robustStreamingManager.setMediaTracks(previewVideoTrack, localAudioTrack);
//         }

//         // 3. Set LiveKit room for protection
//         if (livekitRoom) {
//           robustStreamingManager.setLiveKitRoom(livekitRoom);
//         }

//         console.log('✅ ROBUST background streaming initialized');

//         // 4. Also start service worker as backup
//         if (serviceWorkerReady) {
//           await streamingServiceWorkerManager.startBackgroundStreaming({
//             sessionId: response.session_id || sessionId,
//             livekitToken: response.livekit_token,
//             livekitUrl: response.livekit_url,
//             roomName: response.roomName,
//             streamUrl: response.stream_url,
//             streamingMethod: 'webrtc'
//           });

//           console.log('✅ Service worker backup also started');
//         }

//       } catch (error) {
//         console.warn('⚠️ Failed to start robust background streaming:', error);
//         // Continue with normal streaming even if background protection fails
//       }

//       toast.success('Streaming started successfully');
//     } catch (err) {
//       console.error('❌ Failed to start stream:', err);
//       setStreamStatus('Failed to start stream');
//       dispatch(setError(err.message));
//       toast.error('Failed to start stream');
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   const stopStreaming = async () => {
//     if (streamingMethod === 'rtmp' || rtmpStreaming) {
//       await stopRTMPStreaming();
//       return;
//     }

//     // Default WebRTC streaming stop
//     setIsLoading(true);
//     try {
//       console.log('🛑 Stopping streaming...');
//       streamingActiveRef.current = false; // Clear streaming active flag

//       // Stop ROBUST background streaming
//       try {
//         await robustStreamingManager.stop();
//         console.log('✅ Robust streaming manager stopped');
//       } catch (error) {
//         console.warn('⚠️ Failed to stop robust streaming manager:', error);
//       }

//       // Clean up ROBUST background protection
//       if (window.livekitWSKeepAlive) {
//         clearInterval(window.livekitWSKeepAlive);
//         window.livekitWSKeepAlive = null;
//       }

//       if (window.livekitTrackMonitor) {
//         clearInterval(window.livekitTrackMonitor);
//         window.livekitTrackMonitor = null;
//       }

//       if (window.livekitConnectionMonitor) {
//         clearInterval(window.livekitConnectionMonitor);
//         window.livekitConnectionMonitor = null;
//       }

//       if (window.livekitKeepAliveInterval) {
//         clearInterval(window.livekitKeepAliveInterval);
//         window.livekitKeepAliveInterval = null;
//       }

//       if (window.livekitEngineKeepAlive) {
//         clearInterval(window.livekitEngineKeepAlive);
//         window.livekitEngineKeepAlive = null;
//       }

//       console.log('✅ BULLETPROOF background protection cleaned up');

//       // Stop ULTIMATE background protection utility
//       livekitBackgroundProtection.stop();

//       // Stop BULLETPROOF connection quality monitoring
//       connectionQualityMonitor.stop();

//       // Stop service worker backup
//       if (serviceWorkerReady && backgroundStreamingActive) {
//         try {
//           await streamingServiceWorkerManager.stopBackgroundStreaming();
//           console.log('✅ Service worker backup stopped');
//         } catch (error) {
//           console.warn('⚠️ Failed to stop service worker backup:', error);
//         }
//       }

//       await stopStream({ sessionId }).unwrap();
//       cleanupAllResources();
//       releaseWakeLock();
//       clearTokenRefresh(); // Clear token refresh interval
//       dispatch(setIsStreaming(false));
//       dispatch(clearStreamingData());
//       setStreamStatus('Streaming stopped');
//       toast.success('Streaming stopped - Page will refresh automatically');
//       sessionStorage.removeItem('streamSessionId');

//       // Auto-refresh page after stopping stream
//       setTimeout(() => {
//         console.log('🔄 Auto-refreshing page after stream stop...');
//         window.location.reload();
//       }, 2000); // 2 second delay to show the success message

//     } catch (err) {
//       console.error('❌ Failed to stop stream:', err);
//       streamingActiveRef.current = false; // Clear flag even on error
//       dispatch(setError(err.message));
//       toast.error('Failed to stop stream');
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   // RTMP Streaming Functions
//   const createRTMPIngress = async () => {
//     try {
//       const userId = sessionStorage.getItem('userId');
//       const response = await fetch('http://localhost:8012/api/rtmp-ingress/create', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           session_id: sessionId,
//           teacher_id: userId,
//           teacher_name: `Teacher_${userId}`
//         })
//       });

//       const data = await response.json();
//       if (data.success) {
//         setRtmpConfig(data);
//         setRtmpStatus('RTMP ingress created');
//         toast.success('RTMP ingress created successfully');
//         return data;
//       } else {
//         throw new Error(data.message || 'Failed to create RTMP ingress');
//       }
//     } catch (error) {
//       console.error('❌ Failed to create RTMP ingress:', error);
//       setRtmpStatus('Failed to create RTMP ingress');
//       toast.error('Failed to create RTMP ingress');
//       return null;
//     }
//   };

//   const startRTMPStreaming = async () => {
//     setIsLoading(true);
//     try {
//       // Check permissions first
//       if (!cameraPermissionGranted || !microphonePermissionGranted) {
//         const hasPermissions = await checkMediaPermissions();
//         if (!hasPermissions) return;
//       }

//       // Initialize media tracks
//       if (!previewVideoTrack) {
//         await initializeCameraPreview();
//       }

//       if (!sharedMicrophoneStreamRef.current) {
//         await initializeSharedMicrophone();
//       }

//       // Request wake lock
//       await requestWakeLock();

//       // Create RTMP ingress
//       const rtmpIngressData = await createRTMPIngress();
//       if (!rtmpIngressData) {
//         throw new Error('Failed to create RTMP ingress');
//       }

//       // Create media stream from tracks
//       const mediaStream = new MediaStream();
//       if (previewVideoTrack) {
//         mediaStream.addTrack(previewVideoTrack.mediaStreamTrack);
//       }
//       if (sharedMicrophoneStreamRef.current) {
//         const audioTracks = sharedMicrophoneStreamRef.current.getAudioTracks();
//         audioTracks.forEach(track => mediaStream.addTrack(track));
//       }

//       // Connect to WebSocket for RTMP relay
//       const socket = io('https://sasthra.in');
//       setRtmpSocket(socket);

//       socket.on('connect', () => {
//         console.log('✅ Connected to RTMP relay WebSocket');
//         setRtmpConnected(true);
//         setRtmpStatus('Connected to RTMP relay');
//       });

//       socket.on('rtmp_started', (data) => {
//         console.log('✅ RTMP streaming started:', data);
//         setRtmpStreaming(true);
//         setRtmpStatus('RTMP streaming active');
//         dispatch(setIsStreaming(true));
//         streamingActiveRef.current = true;
//         toast.success('RTMP streaming started successfully');
//       });

//       socket.on('rtmp_error', (data) => {
//         console.error('❌ RTMP error:', data);
//         setRtmpStatus(`RTMP error: ${data.error}`);
//         toast.error(`RTMP error: ${data.error}`);
//       });

//       socket.on('rtmp_stopped', (data) => {
//         console.log('🛑 RTMP streaming stopped:', data);
//         setRtmpStreaming(false);
//         setRtmpStatus('RTMP streaming stopped');
//       });

//       socket.on('disconnect', () => {
//         console.log('🔌 Disconnected from RTMP relay WebSocket');
//         setRtmpConnected(false);
//         setRtmpStatus('Disconnected from RTMP relay');
//       });

//       // Start RTMP streaming via WebRTC to RTMP service
//       const websocketUrl = 'wss://sasthra.in';
//       const success = await webrtcToRtmpService.startRTMPStream(
//         mediaStream,
//         rtmpIngressData,
//         websocketUrl
//       );

//       if (success) {
//         setStreamStatus('RTMP streaming started successfully');
//         setLocalVideoTrack(previewVideoTrack);
//         setupTokenRefresh();

//         // Start background streaming via service worker for RTMP
//         if (serviceWorkerReady) {
//           try {
//             await streamingServiceWorkerManager.startBackgroundStreaming({
//               sessionId: sessionId,
//               livekitToken: null, // RTMP doesn't use LiveKit token
//               livekitUrl: null,
//               roomName: null,
//               streamingMethod: 'rtmp'
//             });

//             console.log('✅ Background RTMP streaming started via service worker');
//           } catch (error) {
//             console.warn('⚠️ Failed to start background RTMP streaming:', error);
//           }
//         }
//       } else {
//         throw new Error('Failed to start RTMP streaming');
//       }

//     } catch (error) {
//       console.error('❌ Failed to start RTMP streaming:', error);
//       setStreamStatus('Failed to start RTMP streaming');
//       setRtmpStatus(`Error: ${error.message}`);
//       toast.error('Failed to start RTMP streaming');
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   const stopRTMPStreaming = async () => {
//     setIsLoading(true);
//     try {
//       console.log('🛑 Stopping RTMP streaming...');
//       streamingActiveRef.current = false;

//       // Stop background streaming via service worker
//       if (serviceWorkerReady && backgroundStreamingActive) {
//         try {
//           await streamingServiceWorkerManager.stopBackgroundStreaming();
//           console.log('✅ Background RTMP streaming stopped via service worker');
//         } catch (error) {
//           console.warn('⚠️ Failed to stop background RTMP streaming:', error);
//         }
//       }

//       // Stop WebRTC to RTMP service
//       webrtcToRtmpService.stopRTMPStream();

//       // Disconnect WebSocket
//       if (rtmpSocket) {
//         rtmpSocket.disconnect();
//         setRtmpSocket(null);
//       }

//       // Delete RTMP ingress
//       if (rtmpConfig) {
//         await fetch('http://localhost:8012/api/rtmp-ingress/delete', {
//           method: 'POST',
//           headers: {
//             'Content-Type': 'application/json',
//           },
//           body: JSON.stringify({
//             session_id: sessionId
//           })
//         });
//       }

//       // Cleanup resources
//       cleanupAllResources();
//       releaseWakeLock();
//       clearTokenRefresh();

//       // Reset states
//       setRtmpConfig(null);
//       setRtmpConnected(false);
//       setRtmpStreaming(false);
//       setRtmpStatus('Ready');
//       dispatch(setIsStreaming(false));
//       dispatch(clearStreamingData());
//       setStreamStatus('RTMP streaming stopped');

//       toast.success('RTMP streaming stopped - Page will refresh automatically');
//       sessionStorage.removeItem('streamSessionId');

//       // Auto-refresh page
//       setTimeout(() => {
//         console.log('🔄 Auto-refreshing page after RTMP stream stop...');
//         window.location.reload();
//       }, 2000);

//     } catch (error) {
//       console.error('❌ Failed to stop RTMP streaming:', error);
//       streamingActiveRef.current = false;
//       toast.error('Failed to stop RTMP streaming');
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   const startScreenShare = async () => {
//     setIsLoading(true);
//     try {
//       console.log('🖥️ Starting screen share...');

//       if (!livekitRoom || !livekitConnected) {
//         throw new Error('LiveKit room not connected');
//       }

//       // Use LiveKit's recommended method for screen sharing
//       await livekitRoom.localParticipant.setScreenShareEnabled(true);

//       setIsScreenSharing(true);
//       setStreamStatus('Screen sharing started');

//       console.log('✅ Screen sharing enabled via LiveKit');
//       toast.success('Screen sharing started');

//     } catch (err) {
//       console.error('❌ Error starting screen share:', err);
//       setStreamStatus('Failed to start screen share');
//       dispatch(setError(err.message));

//       let errorMessage = 'Failed to start screen share';
//       if (err.name === 'NotAllowedError') {
//         errorMessage = 'Screen share permission denied';
//       } else if (err.name === 'NotFoundError') {
//         errorMessage = 'No screen available to share';
//       } else if (err.message.includes('not connected')) {
//         errorMessage = 'Please connect to the room first';
//       }

//       toast.error(errorMessage);

//       // Cleanup on error
//       setIsScreenSharing(false);
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   const stopScreenShare = async () => {
//     setIsLoading(true);
//     try {
//       console.log('🛑 Stopping screen share...');

//       if (livekitRoom && livekitConnected) {
//         // Use LiveKit's recommended method to stop screen sharing
//         await livekitRoom.localParticipant.setScreenShareEnabled(false);
//       }

//       setIsScreenSharing(false);
//       setStreamStatus('Screen sharing stopped');
//       toast.success('Screen sharing stopped');
//     } catch (err) {
//       console.error('❌ Error stopping screen share:', err);
//       setStreamStatus('Error stopping screen share');
//       dispatch(setError(err.message));
//       toast.error('Error stopping screen share');
//       setIsScreenSharing(false);
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   const handleUploadQuiz = async () => {
//     if (!quizFile || !sessionId) {
//       toast.error('Please select a PDF file and start streaming');
//       return;
//     }

//     setIsUploadingQuiz(true);
//     try {
//       const formData = new FormData();
//       formData.append('file', quizFile);
//       const response = await uploadCbtPaper(formData).unwrap();

//       if (response.object_id) {
//         const quizMessage = {
//           session_id: sessionId,
//           message: `🎯 QUIZ_START:${response.object_id} Quiz Started! Click 'Open Quiz' to participate. Total Questions: ${response.questions?.length || 0}`,
//           sender_id: sessionStorage.getItem('userId'),
//           sender_name: sessionStorage.getItem('name') || 'Teacher'
//         };

//         await sendChatMessageMutation(quizMessage);
//         toast.success(`Quiz generated successfully! ID: ${response.object_id}`);
//         setShowQuizUpload(false);
//         setQuizFile(null);
//         setTimeout(loadChatHistory, 500);
//       }
//     } catch (error) {
//       console.error('❌ Quiz upload error:', error);
//       toast.error(`Failed to upload quiz: ${error.message}`);
//     } finally {
//       setIsUploadingQuiz(false);
//     }
//   };

//   const startLiveTranscription = async () => {
//     try {
//       if (!microphonePermissionGranted) {
//         await initializeSharedMicrophone();
//       }

//       const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
//       if (!SpeechRecognition) {
//         throw new Error('Speech recognition not supported');
//       }

//       const recognition = new SpeechRecognition();
//       recognition.continuous = true;
//       recognition.interimResults = true;
//       recognition.lang = availableSourceLanguages.find(lang => lang.code === sourceLanguage)?.code || 'en-US';

//       recognition.onresult = (event) => {
//         const transcript = event.results[event.results.length - 1][0].transcript;
//         setCurrentTranscription(transcript);
//         if (event.results[event.results.length - 1].isFinal) {
//           livekitRoom?.localParticipant.sendText(transcript, { topic: 'transcription' });
//         }
//       };

//       recognition.onstart = () => setTranscriptionStatus('🎤 Listening...');
//       recognition.onerror = (event) => {
//         setTranscriptionStatus(`❌ Transcription error: ${event.error}`);
//         toast.error(`Transcription error: ${event.error}`);
//       };
//       recognition.onend = () => {
//         if (isTranscriptionEnabled) {
//           setTimeout(() => recognition.start(), 100);
//         }
//       };

//       setSpeechRecognition(recognition);
//       recognition.start();
//       setIsTranscriptionEnabled(true);
//       setTranscriptionStatus('🎤 Transcription started');
//       toast.success('Transcription started');
//     } catch (error) {
//       console.error('❌ Transcription error:', error);
//       setTranscriptionStatus(`❌ ${error.message}`);
//       toast.error(error.message);
//     }
//   };

//   const stopLiveTranscription = async () => {
//     try {
//       if (speechRecognition) {
//         speechRecognition.stop();
//         setSpeechRecognition(null);
//       }
//       setIsTranscriptionEnabled(false);
//       setCurrentTranscription('');
//       setTranscriptionStatus('');
//       toast.success('Transcription stopped');
//     } catch (error) {
//       console.error('❌ Transcription stop error:', error);
//       toast.error('Failed to stop transcription');
//     }
//   };

//   const cleanupAllResources = () => {
//     cleanupLiveKitResources();
//     cleanupTranscriptionResources();
//     cleanupSharedMicrophone();
//     clearTokenRefresh(); // Clear token refresh interval
//     setIsScreenSharing(false);
//     setParticipants([]);
//     setJoinedViewers([]);
//   };

//   const cleanupLiveKitResources = () => {
//     [localVideoTrack, localAudioTrack, previewVideoTrack, localScreenTrack, localScreenAudioTrack]
//       .forEach(track => {
//         if (track) {
//           track.stop();
//           track.detach();
//         }
//       });
//     if (screenStream) {
//       screenStream.getTracks().forEach(track => track.stop());
//     }
//     if (livekitRoom) {
//       livekitRoom.disconnect();
//     }
//     setLocalVideoTrack(null);
//     setLocalAudioTrack(null);
//     setPreviewVideoTrack(null);
//     setLocalScreenTrack(null);
//     setLocalScreenAudioTrack(null);
//     setScreenStream(null);
//     setLivekitRoom(null);
//     setLivekitConnected(false);
//     livekitRoomRef.current = null;
//   };

//   const cleanupTranscriptionResources = () => {
//     if (speechRecognition) {
//       speechRecognition.stop();
//       setSpeechRecognition(null);
//     }
//     setIsTranscriptionEnabled(false);
//     setCurrentTranscription('');
//     setTranscriptionStatus('');
//   };

//   const cleanupSharedMicrophone = () => {
//     if (sharedMicrophoneStreamRef.current) {
//       sharedMicrophoneStreamRef.current.getTracks().forEach(track => track.stop());
//       sharedMicrophoneStreamRef.current = null;
//     }
//   };

//   const sendChatMessage = async () => {
//     if (!newMessage.trim() || !socketConnected || !sessionId) return;
//     try {
//       dispatch(setChatLoading(true));
//       await sendChatMessageMutation({
//         session_id: sessionId,
//         message: newMessage.trim(),
//         sender_id: sessionStorage.getItem('userId'),
//         sender_name: sessionStorage.getItem('name') || 'Teacher'
//       });
//       setNewMessage('');
//       setTimeout(loadChatHistory, 500);
//     } catch (error) {
//       dispatch(setChatError(error.message));
//       toast.error('Failed to send message');
//     } finally {
//       dispatch(setChatLoading(false));
//     }
//   };

//   const handleKeyDown = (e) => {
//     if (e.key === 'Enter' && !e.shiftKey) {
//       e.preventDefault();
//       sendChatMessage();
//     }
//   };

//   const toggleChat = () => {
//     setIsChatOpen(!isChatOpen);
//     if (!isChatOpen) {
//       setUnreadMessages(0);
//     }
//   };

//   const formatMessageTime = (timestamp) => {
//     return new Date(timestamp).toLocaleTimeString([], {
//       hour: '2-digit',
//       minute: '2-digit'
//     });
//   };

//   const getRoleColor = (role) => {
//     switch (role) {
//       case 'kota_teacher':
//       case 'faculty':
//         return 'text-emship-600';
//       case 'student':
//         return 'text-blue-600';
//       case 'center_counselor':
//         return 'text-purple-600';
//       default:
//         return 'text-gray-600';
//     }
//   };

//   const getRoleBadge = (role) => {
//     switch (role) {
//       case 'kota_teacher':
//       case 'faculty':
//         return 'Teacher';
//       case 'student':
//         return 'Student';
//       case 'center_counselor':
//         return 'Counselor';
//       default:
//         return 'User';
//     }
//   };

//   return (
//     <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
//       {isLoading && (
//         <motion.div
//           initial={{ opacity: 0 }}
//           animate={{ opacity: 1 }}
//           className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
//         >
//           <motion.div
//             animate={{ rotate: 360 }}
//             transition={{ duration: 1, repeat: Infinity }}
//             className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full"
//           />
//         </motion.div>
//       )}

//       <div className="absolute inset-0 overflow-hidden pointer-events-none">
//         <motion.div
//           animate={{ x: [0, 100, 0], y: [0, -100, 0] }}
//           transition={{ duration: 20, repeat: Infinity, ease: 'linear' }}
//           className="absolute top-10 left-10 w-32 h-32 bg-blue-200/30 rounded-full blur-xl"
//         />
//         <motion.div
//           animate={{ x: [0, -150, 0], y: [0, 100, 0] }}
//           transition={{ duration: 25, repeat: Infinity, ease: 'linear' }}
//           className="absolute top-1/2 right-20 w-40 h-40 bg-indigo-200/20 rounded-full blur-xl"
//         />
//       </div>

//       <div className="max-w-8xl mx-auto p-4 sm:p-6 relative z-10">
//         <motion.div
//           initial={{ opacity: 0, y: -30 }}
//           animate={{ opacity: 1, y: 0 }}
//           className="bg-white/90 backdrop-blur-lg rounded-3xl shadow-2xl border border-white/30 p-8 mb-8"
//         >
//           <div className="relative z-10 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6">
//             <div className="flex items-center space-x-4">
//               <motion.div
//                 whileHover={{ scale: 1.1, rotate: 5 }}
//                 className="w-16 h-16 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg"
//               >
//                 <svg className="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                   <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
//                 </svg>
//               </motion.div>
//               <div>
//                 <h1 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">
//                   Live Streaming Studio
//                 </h1>
//                 <p className="text-gray-600 text-base font-medium mt-1">
//                   Professional Teaching Platform ✨
//                 </p>
//               </div>
//             </div>
//             {isStreaming && (
//               <motion.div
//                 className="flex flex-col sm:flex-row items-start sm:items-center gap-4"
//               >
//                 <select
//                   value={quality}
//                   onChange={(e) => setQuality(e.target.value)}
//                   className="px-5 py-3 bg-white/80 border border-gray-200/50 rounded-2xl focus:outline-none focus:ring-3 focus:ring-blue-500/30"
//                 >
//                   <option value="low">🔹 Low Quality</option>
//                   <option value="medium">🔸 Medium Quality</option>
//                   <option value="high">🔶 High Quality</option>
//                 </select>
//                 <motion.button
//                   whileHover={{ scale: 1.05 }}
//                   onClick={() => setShowQuizUpload(true)}
//                   className="px-8 py-3 bg-gradient-to-r from-purple-500 via-purple-600 to-indigo-600 text-white rounded-2xl"
//                 >
//                   Generate Quiz
//                 </motion.button>
//                 <motion.button
//                   whileHover={{ scale: 1.05 }}
//                   onClick={stopStreaming}
//                   className="px-8 py-3 bg-gradient-to-r from-red-500 via-red-600 to-pink-600 text-white rounded-2xl"
//                 >
//                   Stop Streaming
//                 </motion.button>
//               </motion.div>
//             )}
//           </div>

//           {isStreaming && (
//             <motion.div
//               className="mt-6 p-6 bg-gradient-to-br from-purple-50/80 via-indigo-50/60 to-blue-50/80 rounded-3xl shadow-lg"
//             >
//               <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
//                 <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-6">
//                   <div className="flex items-center space-x-3">
//                     <motion.div
//                       animate={microphoneActive ? { scale: [1, 1.1, 1] } : {}}
//                       className={`p-2 rounded-xl ${microphoneActive ? 'bg-green-100' : 'bg-purple-100'}`}
//                     >
//                       <svg
//                         className={`w-6 h-6 ${microphoneActive ? 'text-green-600' : 'text-purple-600'}`}
//                         fill="none"
//                         viewBox="0 0 24 24"
//                         stroke="currentColor"
//                       >
//                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
//                       </svg>
//                     </motion.div>
//                     <span className="text-purple-800 font-semibold text-lg">
//                       Live Transcription
//                     </span>
//                   </div>
//                   <select
//                     value={sourceLanguage}
//                     onChange={(e) => setSourceLanguage(e.target.value)}
//                     className="px-4 py-2 bg-white/80 border border-purple-300/50 rounded-xl"
//                     disabled={isTranscriptionEnabled}
//                   >
//                     {availableSourceLanguages.map((lang) => (
//                       <option key={lang.code} value={lang.code}>
//                         {lang.flag} {lang.name}
//                       </option>
//                     ))}
//                   </select>
//                   <motion.button
//                     whileHover={{ scale: 1.05 }}
//                     onClick={isTranscriptionEnabled ? stopLiveTranscription : startLiveTranscription}
//                     className={`px-6 py-3 rounded-2xl ${isTranscriptionEnabled ? 'bg-red-500' : 'bg-purple-500'} text-white`}
//                   >
//                     {isTranscriptionEnabled ? 'Stop Transcription' : 'Start Transcription'}
//                   </motion.button>
//                 </div>
//                 {transcriptionStatus && (
//                   <div className="text-sm text-purple-700">{transcriptionStatus}</div>
//                 )}
//               </div>

//               {isTranscriptionEnabled && (
//                 <motion.div
//                   className="mt-4 p-4 bg-white/90 rounded-2xl shadow-lg"
//                 >
//                   <div className="flex items-center justify-between mb-3">
//                     <div className="flex items-center space-x-3">
//                       <motion.div
//                         animate={{ scale: [1, 1.2, 1] }}
//                         className="w-3 h-3 bg-red-500 rounded-full"
//                       />
//                       <span className="text-sm font-bold text-purple-600">LIVE TRANSCRIPTION</span>
//                     </div>
//                   </div>
//                   <div className="min-h-[80px] p-4 bg-gray-50 rounded-xl">
//                     {currentTranscription ? (
//                       <p className="text-base text-gray-800">{currentTranscription}</p>
//                     ) : (
//                       <p className="text-sm text-gray-400 italic">Waiting for speech...</p>
//                     )}
//                   </div>
//                 </motion.div>
//               )}
//             </motion.div>
//           )}
//         </motion.div>

//         {isStreaming ? (
//           <div className="flex flex-col xl:flex-row gap-6">
//             <motion.div className="flex-grow">
//               <div className="bg-white/80 rounded-2xl shadow-xl border border-white/20 overflow-hidden">
//                 <div className="bg-gradient-to-r from-gray-900 to-black" style={{ aspectRatio: '16/9' }}>
//                   {!isScreenSharing ? (
//                     <motion.div className="flex items-center justify-center h-full text-white p-8">
//                       <div className="text-center space-y-6">
//                         <svg className="w-20 h-20 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                           <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
//                         </svg>
//                         <h3 className="text-xl font-semibold">Ready to Share Your Screen</h3>
//                         <motion.button
//                           whileHover={{ scale: 1.05 }}
//                           onClick={startScreenShare}
//                           className="px-8 py-4 bg-blue-600 text-white rounded-xl"
//                         >
//                           Start Screen Share
//                         </motion.button>
//                       </div>
//                     </motion.div>
//                   ) : (
//                     <motion.div className="relative w-full h-full">
//                       <video ref={screenVideoRef} autoPlay playsInline className="w-full h-full object-contain bg-black" />
//                       <motion.div className="absolute bottom-6 right-6 w-56 h-40 bg-black rounded-2xl overflow-hidden border-4 border-white/20">
//                         <video ref={pipCameraRef} autoPlay playsInline muted className="w-full h-full object-cover" />
//                       </motion.div>
//                       <div className="absolute top-6 left-6">
//                         <motion.button
//                           whileHover={{ scale: 1.05 }}
//                           onClick={stopScreenShare}
//                           className="px-6 py-3 bg-red-500 text-white rounded-xl"
//                         >
//                           Stop Screen Share
//                         </motion.button>
//                       </div>
//                     </motion.div>
//                   )}
//                 </div>
//               </div>
//             </motion.div>

//             <motion.div className="w-full xl:w-96 flex-shrink-0 space-y-6">
//               <div className="bg-white/80 rounded-2xl shadow-xl border border-white/20 p-6">
//                 <h3 className="text-lg font-semibold text-gray-800 mb-4">Camera Preview</h3>
//                 <motion.div className="bg-black rounded-xl overflow-hidden" style={{ aspectRatio: '4/3' }}>
//                   <video ref={videoRef} autoPlay playsInline muted className="w-full h-full object-cover" />
//                 </motion.div>
//               </div>

//               <div className="bg-white/80 rounded-2xl shadow-xl border border-white/20">
//                 <div className="flex border-b border-gray-200">
//                   <button
//                     onClick={() => setActiveSidebarTab('status')}
//                     className={`flex-1 p-4 text-sm font-medium ${activeSidebarTab === 'status' ? 'text-blue-600 bg-blue-50' : 'text-gray-600'}`}
//                   >
//                     Status
//                   </button>
//                   <button
//                     onClick={() => setActiveSidebarTab('viewers')}
//                     className={`flex-1 p-4 text-sm font-medium ${activeSidebarTab === 'viewers' ? 'text-blue-600 bg-blue-50' : 'text-gray-600'}`}
//                   >
//                     Viewers ({joinedViewers.length})
//                   </button>
//                 </div>
//                 <div className="p-6">
//                   {activeSidebarTab === 'status' && (
//                     <div className="space-y-3">
//                       <div className="flex items-center justify-between p-3 bg-blue-50 rounded-xl">
//                         <span className="text-sm font-medium text-gray-700">LiveKit</span>
//                         <span className={`text-xs px-2 py-1 rounded-full ${livekitConnected ? 'bg-emerald-100 text-emerald-700' : 'bg-gray-100 text-gray-600'}`}>
//                           {livekitConnected ? 'Connected' : 'Disconnected'}
//                         </span>
//                       </div>
//                       <div className="flex items-center justify-between p-3 bg-purple-50 rounded-xl">
//                         <span className="text-sm font-medium text-gray-700">Camera</span>
//                         <span className={`text-xs px-2 py-1 rounded-full ${(cameraPermissionGranted && localVideoTrack) ? 'bg-emerald-100 text-emerald-700' : 'bg-gray-100 text-gray-600'}`}>
//                           {(cameraPermissionGranted && localVideoTrack) ? 'Published' : 'Not Published'}
//                         </span>
//                       </div>
//                       <div className="flex items-center justify-between p-3 bg-indigo-50 rounded-xl">
//                         <span className="text-sm font-medium text-gray-700">Screen Share</span>
//                         <span className={`text-xs px-2 py-1 rounded-full ${(isScreenSharing && localScreenTrack) ? 'bg-emerald-100 text-emerald-700' : 'bg-gray-100 text-gray-600'}`}>
//                           {(isScreenSharing && localScreenTrack) ? 'Active' : 'Inactive'}
//                         </span>
//                       </div>
//                       <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
//                         <div className="flex items-center space-x-2">
//                           <span className="text-sm font-medium text-gray-700">Background Streaming</span>
//                           {backgroundStreamingActive && (
//                             <motion.div
//                               animate={{ scale: [1, 1.2, 1] }}
//                               transition={{ duration: 2, repeat: Infinity }}
//                               className="w-2 h-2 bg-green-500 rounded-full"
//                             />
//                           )}
//                         </div>
//                         <span className={`text-xs px-2 py-1 rounded-full ${backgroundStreamingActive ? 'bg-emerald-100 text-emerald-700' : 'bg-gray-100 text-gray-600'}`}>
//                           {backgroundStreamingActive ? 'Active' : 'Inactive'}
//                         </span>
//                       </div>
//                       {serviceWorkerReady && (
//                         <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl">
//                           <div className="flex items-center space-x-2">
//                             <span className="text-sm font-medium text-gray-700">Service Worker</span>
//                             <motion.div
//                               animate={{ rotate: 360 }}
//                               transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
//                               className="w-3 h-3"
//                             >
//                               <svg className="w-3 h-3 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                                 <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
//                                 <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
//                               </svg>
//                             </motion.div>
//                           </div>
//                           <span className="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-700">
//                             Ready
//                           </span>
//                         </div>
//                       )}
//                       {lastServiceWorkerHeartbeat && (
//                         <div className="p-3 bg-gray-50 rounded-xl">
//                           <div className="flex items-center justify-between mb-2">
//                             <span className="text-xs font-medium text-gray-600">Last Heartbeat</span>
//                             <span className="text-xs text-gray-500">
//                               {new Date(lastServiceWorkerHeartbeat).toLocaleTimeString()}
//                             </span>
//                           </div>
//                           {serviceWorkerState && (
//                             <div className="text-xs text-gray-500">
//                               Connection: {serviceWorkerState.connectionState}
//                             </div>
//                           )}
//                         </div>
//                       )}
//                     </div>
//                   )}
//                   {activeSidebarTab === 'viewers' && (
//                     <div className="space-y-3 max-h-64 overflow-y-auto">
//                       {joinedViewers.map((viewer) => (
//                         <div key={viewer.viewer_id} className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
//                           <div className="flex items-center space-x-3">
//                             <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white">
//                               {viewer.viewer_name?.charAt(0)?.toUpperCase() || 'U'}
//                             </div>
//                             <div>
//                               <p className="text-sm font-medium text-gray-800">{viewer.viewer_name}</p>
//                               <span className={`text-xs px-2 py-0.5 rounded-full ${getRoleColor(viewer.user_role)} bg-opacity-10 bg-current`}>
//                                 {getRoleBadge(viewer.user_role)}
//                               </span>
//                             </div>
//                           </div>
//                         </div>
//                       ))}
//                     </div>
//                   )}
//                 </div>
//               </div>
//             </motion.div>

//             <motion.button
//               whileHover={{ scale: 1.1 }}
//               onClick={toggleChat}
//               className="fixed bottom-6 right-6 bg-blue-600 text-white p-4 rounded-full shadow-2xl"
//             >
//               <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                 <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.405L3 21l2.595-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
//               </svg>
//               {unreadMessages > 0 && !isChatOpen && (
//                 <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-6 h-6 rounded-full flex items-center justify-center">
//                   {unreadMessages}
//                 </span>
//               )}
//             </motion.button>

//             {isChatOpen && (
//               <motion.div
//                 initial={{ opacity: 0, y: 20 }}
//                 animate={{ opacity: 1, y: 0 }}
//                 className="fixed bottom-24 right-6 w-96 h-[500px] bg-white/95 rounded-2xl shadow-2xl border border-white/20 flex flex-col"
//               >
//                 <div className="flex items-center justify-between p-4 border-b border-gray-200">
//                   <h3 className="text-lg font-semibold text-gray-800">Live Chat</h3>
//                   <motion.button
//                     whileHover={{ scale: 1.1 }}
//                     onClick={toggleChat}
//                     className="text-gray-500"
//                   >
//                     <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
//                     </svg>
//                   </motion.button>
//                 </div>
//                 <div ref={chatContainerRef} className="flex-grow p-4 overflow-y-auto space-y-3">
//                   {chatMessages.map((message, index) => (
//                     <motion.div
//                       key={`${message.id || 'msg'}-${index}`}
//                       initial={{ opacity: 0, y: 10 }}
//                       animate={{ opacity: 1, y: 0 }}
//                       className="bg-white rounded-xl p-3 shadow-sm border"
//                     >
//                       <div className="flex items-start justify-between">
//                         <div className="flex-1">
//                           <span className={`text-sm font-semibold ${getRoleColor(message.sender_role)}`}>
//                             {message.sender_name}
//                           </span>
//                           <p className="text-sm text-gray-800">{message.message}</p>
//                         </div>
//                         <span className="text-xs text-gray-400">{formatMessageTime(message.timestamp)}</span>
//                       </div>
//                     </motion.div>
//                   ))}
//                 </div>
//                 <div className="p-4 border-t border-gray-200 flex space-x-2">
//                   <input
//                     type="text"
//                     value={newMessage}
//                     onChange={(e) => setNewMessage(e.target.value)}
//                     onKeyDown={handleKeyDown}
//                     placeholder="Type a message..."
//                     className="flex-1 px-4 py-2 bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500"
//                     disabled={!socketConnected}
//                   />
//                   <motion.button
//                     whileHover={{ scale: 1.05 }}
//                     onClick={sendChatMessage}
//                     disabled={!newMessage.trim() || !socketConnected}
//                     className="p-3 bg-blue-600 text-white rounded-xl disabled:bg-gray-400"
//                   >
//                     <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
//                     </svg>
//                   </motion.button>
//                 </div>
//               </motion.div>
//             )}

//             {showQuizUpload && (
//               <motion.div
//                 initial={{ opacity: 0 }}
//                 animate={{ opacity: 1 }}
//                 className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
//                 onClick={() => setShowQuizUpload(false)}
//               >
//                 <motion.div
//                   initial={{ scale: 0.9 }}
//                   animate={{ scale: 1 }}
//                   className="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl"
//                   onClick={(e) => e.stopPropagation()}
//                 >
//                   <div className="flex items-center justify-between mb-6">
//                     <h3 className="text-2xl font-bold text-gray-800">Generate Quiz</h3>
//                     <motion.button
//                       whileHover={{ scale: 1.1 }}
//                       onClick={() => setShowQuizUpload(false)}
//                       className="text-gray-500"
//                     >
//                       <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                         <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
//                       </svg>
//                     </motion.button>
//                   </div>
//                   <div className="space-y-4">
//                     <div>
//                       <label className="block text-sm font-medium text-gray-700 mb-2">
//                         Upload PDF File
//                       </label>
//                       <input
//                         type="file"
//                         accept=".pdf"
//                         onChange={(e) => setQuizFile(e.target.files[0])}
//                         className="w-full px-4 py-3 border border-gray-300 rounded-xl"
//                       />
//                       {quizFile && (
//                         <p className="text-sm text-green-600 mt-2">✅ {quizFile.name} selected</p>
//                       )}
//                     </div>
//                     <div className="flex space-x-3 pt-4">
//                       <motion.button
//                         whileHover={{ scale: 1.05 }}
//                         onClick={handleUploadQuiz}
//                         disabled={!quizFile || isUploadingQuiz}
//                         className="flex-1 px-6 py-3 bg-purple-500 text-white rounded-xl disabled:opacity-50"
//                       >
//                         {isUploadingQuiz ? 'Uploading...' : 'Generate Quiz'}
//                       </motion.button>
//                       <motion.button
//                         whileHover={{ scale: 1.05 }}
//                         onClick={() => setShowQuizUpload(false)}
//                         className="px-6 py-3 bg-gray-200 text-gray-800 rounded-xl"
//                       >
//                         Cancel
//                       </motion.button>
//                     </div>
//                   </div>
//                 </motion.div>
//               </motion.div>
//             )}
//           </div>
//         ) : (
//           <motion.div
//             className="bg-white/90 rounded-3xl shadow-2xl border border-white/30 p-16 text-center max-w-3xl mx-auto"
//           >
//             <motion.div
//               whileHover={{ scale: 1.1 }}
//               className="w-32 h-32 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-8"
//             >
//               <svg className="w-16 h-16 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                 <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
//               </svg>
//             </motion.div>
//             <h2 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent mb-6">
//               Ready to Go Live? ✨
//             </h2>
//             <p className="text-gray-600 mb-10 text-xl font-medium">{streamStatus}</p>
//             {error && (
//               <div className="mb-8 p-6 bg-red-50 rounded-2xl text-red-700">
//                 <span className="font-medium">{error}</span>
//               </div>
//             )}

//             {/* Streaming Method Selector */}
//             <div className="mb-8">
//               <div className="flex items-center justify-center mb-4">
//                 <button
//                   onClick={() => setShowStreamingMethodSelector(!showStreamingMethodSelector)}
//                   className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors"
//                 >
//                   <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//                     <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
//                     <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
//                   </svg>
//                   <span className="font-medium">Streaming Method: {streamingMethod === 'rtmp' ? 'RTMP (Background Persistent)' : 'WebRTC (Standard)'}</span>
//                   <svg className={`w-4 h-4 transition-transform ${showStreamingMethodSelector ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
//                     <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
//                   </svg>
//                 </button>
//               </div>

//               {showStreamingMethodSelector && (
//                 <motion.div
//                   initial={{ opacity: 0, y: -10 }}
//                   animate={{ opacity: 1, y: 0 }}
//                   className="bg-white rounded-2xl border-2 border-gray-100 p-6 shadow-lg"
//                 >
//                   <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                     {/* WebRTC Option */}
//                     <motion.div
//                       whileHover={{ scale: 1.02 }}
//                       onClick={() => setStreamingMethod('webrtc')}
//                       className={`p-4 rounded-xl border-2 cursor-pointer transition-all ${
//                         streamingMethod === 'webrtc'
//                           ? 'border-blue-500 bg-blue-50'
//                           : 'border-gray-200 hover:border-gray-300'
//                       }`}
//                     >
//                       <div className="flex items-center space-x-3 mb-2">
//                         <div className={`w-4 h-4 rounded-full border-2 ${
//                           streamingMethod === 'webrtc' ? 'border-blue-500 bg-blue-500' : 'border-gray-300'
//                         }`}>
//                           {streamingMethod === 'webrtc' && (
//                             <div className="w-2 h-2 bg-white rounded-full m-0.5"></div>
//                           )}
//                         </div>
//                         <h3 className="font-bold text-gray-800">WebRTC (Standard)</h3>
//                       </div>
//                       <p className="text-sm text-gray-600 mb-2">Direct browser streaming</p>
//                       <div className="text-xs text-gray-500">
//                         <div className="flex items-center space-x-1 mb-1">
//                           <span className="text-green-500">✓</span>
//                           <span>Quick setup</span>
//                         </div>
//                         <div className="flex items-center space-x-1 mb-1">
//                           <span className="text-green-500">✓</span>
//                           <span>Low latency</span>
//                         </div>
//                         <div className="flex items-center space-x-1">
//                           <span className="text-red-500">⚠</span>
//                           <span>May stop on tab switch</span>
//                         </div>
//                       </div>
//                     </motion.div>

//                     {/* RTMP Option */}
//                     <motion.div
//                       whileHover={{ scale: 1.02 }}
//                       onClick={() => setStreamingMethod('rtmp')}
//                       className={`p-4 rounded-xl border-2 cursor-pointer transition-all ${
//                         streamingMethod === 'rtmp'
//                           ? 'border-purple-500 bg-purple-50'
//                           : 'border-gray-200 hover:border-gray-300'
//                       }`}
//                     >
//                       <div className="flex items-center space-x-3 mb-2">
//                         <div className={`w-4 h-4 rounded-full border-2 ${
//                           streamingMethod === 'rtmp' ? 'border-purple-500 bg-purple-500' : 'border-gray-300'
//                         }`}>
//                           {streamingMethod === 'rtmp' && (
//                             <div className="w-2 h-2 bg-white rounded-full m-0.5"></div>
//                           )}
//                         </div>
//                         <h3 className="font-bold text-gray-800">RTMP (Background Persistent)</h3>
//                       </div>
//                       <p className="text-sm text-gray-600 mb-2">Server-side streaming relay</p>
//                       <div className="text-xs text-gray-500">
//                         <div className="flex items-center space-x-1 mb-1">
//                           <span className="text-green-500">✓</span>
//                           <span>Background persistent</span>
//                         </div>
//                         <div className="flex items-center space-x-1 mb-1">
//                           <span className="text-green-500">✓</span>
//                           <span>Long sessions (3-4+ hours)</span>
//                         </div>
//                         <div className="flex items-center space-x-1">
//                           <span className="text-green-500">✓</span>
//                           <span>Tab-switch resistant</span>
//                         </div>
//                       </div>
//                     </motion.div>
//                   </div>

//                   {streamingMethod === 'rtmp' && (
//                     <div className="mt-4 p-3 bg-purple-50 rounded-lg border border-purple-200">
//                       <div className="flex items-center space-x-2 text-purple-700">
//                         <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//                           <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
//                         </svg>
//                         <span className="text-sm font-medium">RTMP Status: {rtmpStatus}</span>
//                       </div>
//                     </div>
//                   )}
//                 </motion.div>
//               )}
//             </div>

//             <motion.button
//               whileHover={{ scale: 1.05 }}
//               onClick={startStreaming}
//               disabled={isLoading}
//               className="px-16 py-5 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white rounded-3xl font-bold text-xl disabled:opacity-50"
//             >
//               {streamingMethod === 'rtmp' ? 'Start RTMP Streaming' : 'Start Live Streaming'}
//             </motion.button>
//           </motion.div>
//         )}
//       </div>
//       <ToastContainer position="top-right" autoClose={5000} />
//     </div>
//   );
// };

// export default TeacherLiveStreaming;